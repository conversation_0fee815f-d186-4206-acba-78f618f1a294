# Dialplate模块 - C语言版本

这是原始C++版本Dialplate模块的C语言移植版本，实现了完整的运动表盘功能，包括速度显示、运动数据统计、录制控制等。

## 特性

- ✅ **MVP架构** - 模型(Model)、视图(View)、控制器(Presenter)分离
- ✅ **运动数据显示** - 实时速度、平均速度、运动时间、距离、卡路里
- ✅ **录制控制** - 开始/暂停/继续/停止录制功能
- ✅ **GPS支持** - GPS状态检测和位置信息
- ✅ **动画效果** - 页面显示动画和按钮交互动画
- ✅ **页面导航** - 支持跳转到地图页面和系统信息页面
- ✅ **状态管理** - 完整的录制状态机
- ✅ **音效反馈** - 操作音效提示

## 架构设计

### C++版本 → C语言版本转换

| C++特性 | C语言实现 |
|---------|-----------|
| `class Dialplate` | `struct dialplate_c` |
| `class DialplateModel` | `struct dialplate_model_c` |
| `class DialplateView` | `struct dialplate_view_c` |
| 虚函数 | 函数指针 |
| 继承 | 结构体组合 |
| 私有成员 | 命名约定 |
| 构造/析构 | create/destroy函数 |

### 模块结构

```
dialplate_c.h              # 主头文件，包含所有结构和函数声明
├── dialplate_c.c           # 表盘主类实现
├── dialplate_model_c.c     # 数据模型实现
├── dialplate_view_c.c      # 视图UI实现
├── dialplate_example_c.c   # 使用示例
└── Makefile_dialplate_c    # 编译配置
```

## 快速开始

### 1. 编译项目

```bash
# 使用提供的Makefile编译
make -f Makefile_dialplate_c all

# 或者手动编译
gcc -o dialplate_example *.c -llvgl -I.
```

### 2. 基本使用

```c
#include "dialplate_c.h"

// 1. 创建页面工厂和管理器
pm_page_factory_t* factory = pm_page_factory_create();
pm_page_factory_set_create_func(factory, your_page_factory);

pm_page_manager_t* manager = pm_page_manager_create(factory);

// 2. 安装表盘页面
pm_page_manager_install(manager, "Dialplate", "Dialplate");

// 3. 显示表盘
pm_page_manager_push(manager, "Dialplate", NULL);
```

### 3. 创建表盘页面

```c
// 页面工厂函数
static pm_page_base_t* create_dialplate_page(const char* name) {
    if (strcmp(name, "Dialplate") == 0) {
        dialplate_c_t* dialplate = dialplate_create();
        return &dialplate->base;
    }
    return NULL;
}
```

## 核心功能

### 1. 运动数据管理

```c
// 更新运动状态
dialplate_sport_status_t status = {
    .speed_kph = 15.5f,
    .speed_avg_kph = 12.8f,
    .single_time = 1800,        // 30分钟
    .single_distance = 6400,    // 6.4公里
    .single_calorie = 320       // 320卡路里
};

dialplate_model_update_sport_status(&model, &status);
```

### 2. GPS信息管理

```c
// 更新GPS信息
dialplate_gps_info_t gps = {
    .satellites = 8,
    .is_valid = true,
    .latitude = 39.9042,
    .longitude = 116.4074,
    .altitude = 43.5f
};

dialplate_model_update_gps_info(&model, &gps);
```

### 3. 录制控制

```c
// 录制状态机
typedef enum {
    DIALPLATE_RECORD_STATE_READY,   // 准备状态
    DIALPLATE_RECORD_STATE_RUN,     // 运行状态
    DIALPLATE_RECORD_STATE_PAUSE,   // 暂停状态
    DIALPLATE_RECORD_STATE_STOP     // 停止状态
} dialplate_record_state_t;

// 发送录制命令
dialplate_model_recorder_command(&model, DIALPLATE_REC_START);
```

### 4. UI更新

```c
// 更新速度显示
dialplate_view_update_speed(&view, 25);

// 更新信息显示
dialplate_view_update_info(&view, 0, "12.8 km/h");  // 平均速度
dialplate_view_update_info(&view, 1, "30:15");      // 运动时间
dialplate_view_update_info(&view, 2, "6.4 km");     // 运动距离
dialplate_view_update_info(&view, 3, "320 k");      // 卡路里
```

## 界面布局

```
┌─────────────────────────────────┐
│          速度显示区域            │
│            25 km/h              │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│  AVG    TIME    DIST    CAL     │
│ 12.8   30:15   6.4km   320k     │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│   [MAP]  [REC]  [MENU]          │
└─────────────────────────────────┘
```

## 按钮操作

- **地图按钮** - 跳转到地图页面
- **录制按钮** - 控制运动录制
  - 长按：开始录制（需要GPS准备好）
  - 短按：暂停/继续录制
  - 长按（暂停状态）：准备停止
  - 长按（停止状态）：确认停止
- **菜单按钮** - 跳转到系统信息页面

## 录制状态机

```
READY ──长按──> RUN ──短按──> PAUSE
  ↑                            ↓
  └──长按──< STOP <──长按──────┘
              ↑
              └──短按──> RUN
```

## 动画效果

- **页面进入动画**
  - 顶部信息从上方滑入
  - 底部信息从下方滑入并淡入
  - 按钮依次展开
- **按钮交互动画**
  - 按下时缩放效果
  - 焦点时外边框高亮

## 数据流

```
DataProc系统 → Model → View → UI显示
     ↓
   录制控制
     ↓
   音效反馈
```

## 配置选项

在 `dialplate_c.h` 中可以配置：

```c
// 录制状态
typedef enum {
    DIALPLATE_RECORD_STATE_READY,
    DIALPLATE_RECORD_STATE_RUN,
    DIALPLATE_RECORD_STATE_PAUSE,
    DIALPLATE_RECORD_STATE_STOP
} dialplate_record_state_t;

// 录制命令
typedef enum {
    DIALPLATE_REC_START,
    DIALPLATE_REC_PAUSE,
    DIALPLATE_REC_CONTINUE,
    DIALPLATE_REC_STOP,
    DIALPLATE_REC_READY_STOP
} dialplate_rec_cmd_t;
```

## 依赖关系

- **LVGL** - 图形界面库
- **PageManager** - 页面管理框架
- **DataProc** - 数据处理系统（可选，用于实际数据源）

## 编译和测试

```bash
# 编译所有目标
make -f Makefile_dialplate_c all

# 运行测试
make -f Makefile_dialplate_c test

# 运行示例（需要LVGL环境）
make -f Makefile_dialplate_c run-example

# 清理构建文件
make -f Makefile_dialplate_c clean
```

## 示例代码

查看 `dialplate_example_c.c` 获取完整的使用示例，包括：
- 页面工厂实现
- 数据模拟生成
- 事件处理
- 状态管理

## 注意事项

1. **内存管理** - 确保正确调用create/destroy函数对
2. **LVGL版本** - 需要LVGL 8.0+版本
3. **资源管理** - 图片和字体资源需要根据实际系统实现
4. **数据源** - 示例使用模拟数据，实际使用需要连接真实数据源
5. **线程安全** - 在多线程环境中需要额外同步

## 与C++版本的兼容性

C语言版本在功能上与C++版本完全兼容，主要差异：
- 使用结构体和函数指针替代类和虚函数
- 手动内存管理替代RAII
- 显式的create/destroy函数调用
- 函数式编程风格替代面向对象

## 许可证

MIT License - 与原始C++版本相同
