/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Dialplate Usage Example
 */
#include "dialplate_c.h"
#include "pm_log_c.h"
#include <stdio.h>
#include <stdlib.h>

/* 全局变量 */
static pm_page_manager_t* g_page_manager = NULL;
static pm_page_factory_t* g_page_factory = NULL;
static dialplate_c_t* g_dialplate_instance = NULL;

/* 模拟数据更新定时器 */
static lv_timer_t* g_data_timer = NULL;

/* ========== 页面工厂实现 ========== */

/**
 * @brief 页面工厂创建函数
 * @param name 页面类名
 * @retval 页面基类指针
 */
static pm_page_base_t* dialplate_page_factory_create(const char* name)
{
    if (name == NULL) {
        return NULL;
    }
    
    PM_LOG_INFO("Creating page: %s", name);
    
    if (strcmp(name, "Dialplate") == 0) {
        /* 创建表盘页面 */
        dialplate_c_t* dialplate = dialplate_create();
        if (dialplate != NULL) {
            g_dialplate_instance = dialplate; // 保存实例引用
            return &dialplate->base;
        }
    } else if (strcmp(name, "LiveMap") == 0) {
        /* 创建地图页面（简化实现） */
        pm_page_base_t* page = pm_page_base_create();
        if (page != NULL) {
            PM_LOG_INFO("LiveMap page created (simplified)");
            return page;
        }
    } else if (strcmp(name, "SystemInfos") == 0) {
        /* 创建系统信息页面（简化实现） */
        pm_page_base_t* page = pm_page_base_create();
        if (page != NULL) {
            PM_LOG_INFO("SystemInfos page created (simplified)");
            return page;
        }
    }
    
    PM_LOG_ERROR("Unknown page type: %s", name);
    return NULL;
}

/* ========== 模拟数据生成 ========== */

/**
 * @brief 模拟数据更新定时器回调
 * @param timer 定时器指针
 */
static void data_update_timer_cb(lv_timer_t* timer)
{
    if (g_dialplate_instance == NULL) {
        return;
    }
    
    static uint32_t time_counter = 0;
    static float speed_base = 15.0f;
    static float distance_total = 0.0f;
    
    time_counter += 5; // 每5秒更新一次
    
    /* 生成模拟运动数据 */
    dialplate_sport_status_t sport_status = {0};
    
    /* 模拟速度变化 */
    sport_status.speed_kph = speed_base + (rand() % 10 - 5) * 0.5f;
    if (sport_status.speed_kph < 0) sport_status.speed_kph = 0;
    
    /* 计算平均速度 */
    sport_status.speed_avg_kph = speed_base * 0.8f;
    
    /* 更新时间和距离 */
    sport_status.single_time = time_counter;
    distance_total += sport_status.speed_kph * (5.0f / 3600.0f) * 1000.0f; // 转换为米
    sport_status.single_distance = (uint32_t)distance_total;
    
    /* 计算卡路里（简化公式） */
    sport_status.single_calorie = (uint32_t)(distance_total * 0.05f);
    
    /* 设置总计数据 */
    sport_status.total_time = time_counter + 3600; // 假设之前已运动1小时
    sport_status.total_distance = sport_status.single_distance + 10000; // 假设之前已运动10km
    sport_status.total_calorie = sport_status.single_calorie + 500; // 假设之前已消耗500卡
    
    /* 更新模型数据 */
    dialplate_model_update_sport_status(&g_dialplate_instance->model, &sport_status);
    
    /* 生成模拟GPS数据 */
    dialplate_gps_info_t gps_info = {
        .satellites = 6 + (rand() % 5), // 6-10颗卫星
        .is_valid = true,
        .latitude = 39.9042 + (rand() % 100 - 50) * 0.0001, // 北京附近
        .longitude = 116.4074 + (rand() % 100 - 50) * 0.0001,
        .altitude = 43.5f + (rand() % 20 - 10) * 0.1f
    };
    
    dialplate_model_update_gps_info(&g_dialplate_instance->model, &gps_info);
    
    PM_LOG_INFO("Data updated: speed=%.1f km/h, distance=%.1f km, time=%d s", 
                sport_status.speed_kph, sport_status.single_distance / 1000.0f, sport_status.single_time);
}

/* ========== 初始化和清理函数 ========== */

/**
 * @brief 初始化表盘示例
 * @retval 0：成功，-1：失败
 */
int dialplate_example_init(void)
{
    PM_LOG_INFO("Initializing Dialplate Example...");
    
    /* 创建页面工厂 */
    g_page_factory = pm_page_factory_create();
    if (g_page_factory == NULL) {
        PM_LOG_ERROR("Failed to create page factory");
        return -1;
    }
    
    /* 设置页面创建函数 */
    pm_page_factory_set_create_func(g_page_factory, dialplate_page_factory_create);
    
    /* 创建页面管理器 */
    g_page_manager = pm_page_manager_create(g_page_factory);
    if (g_page_manager == NULL) {
        PM_LOG_ERROR("Failed to create page manager");
        pm_page_factory_destroy(g_page_factory);
        return -1;
    }
    
    /* 设置全局动画 */
    pm_page_manager_set_global_load_anim_type(
        g_page_manager,
        PM_LOAD_ANIM_NONE, // 表盘页面使用无动画
        0,
        NULL
    );
    
    /* 安装页面 */
    if (!pm_page_manager_install(g_page_manager, "Dialplate", "Dialplate")) {
        PM_LOG_ERROR("Failed to install Dialplate page");
        return -1;
    }
    
    if (!pm_page_manager_install(g_page_manager, "LiveMap", "LiveMap")) {
        PM_LOG_ERROR("Failed to install LiveMap page");
        return -1;
    }
    
    if (!pm_page_manager_install(g_page_manager, "SystemInfos", "SystemInfos")) {
        PM_LOG_ERROR("Failed to install SystemInfos page");
        return -1;
    }
    
    /* 推入表盘页面作为主页面 */
    if (!pm_page_manager_push(g_page_manager, "Dialplate", NULL)) {
        PM_LOG_ERROR("Failed to push Dialplate page");
        return -1;
    }
    
    /* 创建数据更新定时器 */
    g_data_timer = lv_timer_create(data_update_timer_cb, 5000, NULL); // 每5秒更新一次
    
    PM_LOG_INFO("Dialplate Example initialized successfully!");
    return 0;
}

/**
 * @brief 清理表盘示例
 */
void dialplate_example_deinit(void)
{
    PM_LOG_INFO("Deinitializing Dialplate Example...");
    
    /* 删除数据更新定时器 */
    if (g_data_timer) {
        lv_timer_del(g_data_timer);
        g_data_timer = NULL;
    }
    
    /* 清理页面管理器 */
    if (g_page_manager) {
        pm_page_manager_destroy(g_page_manager);
        g_page_manager = NULL;
    }
    
    /* 清理页面工厂 */
    if (g_page_factory) {
        pm_page_factory_destroy(g_page_factory);
        g_page_factory = NULL;
    }
    
    g_dialplate_instance = NULL;
    
    PM_LOG_INFO("Dialplate Example deinitialized");
}

/* ========== 控制函数 ========== */

/**
 * @brief 模拟开始运动
 */
void dialplate_example_start_sport(void)
{
    if (g_dialplate_instance == NULL) {
        PM_LOG_WARN("Dialplate instance not available");
        return;
    }
    
    PM_LOG_INFO("Starting sport simulation...");
    
    /* 生成初始测试数据 */
    dialplate_model_generate_test_data(&g_dialplate_instance->model);
    
    /* 启动数据更新定时器 */
    if (g_data_timer) {
        lv_timer_resume(g_data_timer);
    }
}

/**
 * @brief 模拟停止运动
 */
void dialplate_example_stop_sport(void)
{
    if (g_dialplate_instance == NULL) {
        PM_LOG_WARN("Dialplate instance not available");
        return;
    }
    
    PM_LOG_INFO("Stopping sport simulation...");
    
    /* 暂停数据更新定时器 */
    if (g_data_timer) {
        lv_timer_pause(g_data_timer);
    }
}

/**
 * @brief 模拟录制控制
 * @param long_press 是否为长按
 */
void dialplate_example_record_control(bool long_press)
{
    if (g_dialplate_instance == NULL) {
        PM_LOG_WARN("Dialplate instance not available");
        return;
    }
    
    PM_LOG_INFO("Record control: %s", long_press ? "long press" : "short press");
    
    /* 调用录制控制函数 */
    dialplate_on_record(g_dialplate_instance, long_press);
}

/**
 * @brief 获取当前表盘状态
 * @retval 录制状态
 */
dialplate_record_state_t dialplate_example_get_record_state(void)
{
    if (g_dialplate_instance == NULL) {
        return DIALPLATE_RECORD_STATE_READY;
    }
    
    return g_dialplate_instance->rec_state;
}

/**
 * @brief 获取当前运动数据
 * @param status 运动状态结构指针
 * @retval true：成功获取，false：失败
 */
bool dialplate_example_get_sport_status(dialplate_sport_status_t* status)
{
    if (g_dialplate_instance == NULL || status == NULL) {
        return false;
    }
    
    *status = g_dialplate_instance->model.sport_status_info;
    return true;
}

/* ========== 测试函数 ========== */

/**
 * @brief 运行表盘示例测试
 */
void dialplate_example_run_test(void)
{
    PM_LOG_INFO("Running Dialplate Example Test...");
    
    /* 初始化示例 */
    if (dialplate_example_init() != 0) {
        PM_LOG_ERROR("Failed to initialize example");
        return;
    }
    
    /* 开始运动模拟 */
    dialplate_example_start_sport();
    
    PM_LOG_INFO("Dialplate Example Test started. Use LVGL simulator to interact.");
    PM_LOG_INFO("- Short press record button to pause/continue");
    PM_LOG_INFO("- Long press record button to start/stop recording");
    PM_LOG_INFO("- Press map button to navigate to map page");
    PM_LOG_INFO("- Press menu button to navigate to system info page");
}

/* ========== 主函数（用于独立测试） ========== */

#ifdef DIALPLATE_STANDALONE_TEST
int main(void)
{
    /* 初始化LVGL（这里需要根据实际平台实现） */
    // lv_init();
    // ... 平台相关的初始化代码
    
    /* 运行表盘示例测试 */
    dialplate_example_run_test();
    
    /* 主循环（这里需要根据实际平台实现） */
    // while(1) {
    //     lv_timer_handler();
    //     lv_tick_inc(5);
    //     usleep(5000);
    // }
    
    /* 清理 */
    dialplate_example_deinit();
    
    return 0;
}
#endif
