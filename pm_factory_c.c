/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Page Factory Implementation
 */
#include "page_manager_c.h"
#include "pm_log_c.h"

/**
 * @brief  创建页面工厂
 * @retval 页面工厂指针，失败返回NULL
 */
pm_page_factory_t* pm_page_factory_create(void)
{
    pm_page_factory_t* factory = (pm_page_factory_t*)malloc(sizeof(pm_page_factory_t));
    if (factory == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for page factory");
        return NULL;
    }
    
    /* 初始化成员变量 */
    memset(factory, 0, sizeof(pm_page_factory_t));
    factory->create_page = NULL;
    
    PM_LOG_INFO("Page factory created");
    return factory;
}

/**
 * @brief  销毁页面工厂
 * @param  factory: 页面工厂指针
 * @retval None
 */
void pm_page_factory_destroy(pm_page_factory_t* factory)
{
    if (factory == NULL) {
        return;
    }
    
    free(factory);
    PM_LOG_INFO("Page factory destroyed");
}

/**
 * @brief  设置页面创建函数
 * @param  factory: 页面工厂指针
 * @param  create_func: 页面创建函数指针
 * @retval None
 */
void pm_page_factory_set_create_func(pm_page_factory_t* factory, 
    pm_page_base_t* (*create_func)(const char* name))
{
    if (factory == NULL) {
        PM_LOG_ERROR("Factory is NULL");
        return;
    }
    
    factory->create_page = create_func;
    PM_LOG_INFO("Page factory create function set");
}

/**
 * @brief  创建页面
 * @param  factory: 页面工厂指针
 * @param  name: 页面类名
 * @retval 页面基类指针，失败返回NULL
 */
pm_page_base_t* pm_page_factory_create_page(pm_page_factory_t* factory, const char* name)
{
    if (factory == NULL || name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return NULL;
    }
    
    if (factory->create_page == NULL) {
        PM_LOG_ERROR("Create function is not set");
        return NULL;
    }
    
    pm_page_base_t* page = factory->create_page(name);
    if (page == NULL) {
        PM_LOG_ERROR("Failed to create page: %s", name);
        return NULL;
    }
    
    PM_LOG_INFO("Page(%s) created by factory", name);
    return page;
}
