/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Dialplate Module
 *
 * 表盘模块C语言版本
 * 实现了运动表盘的完整功能，包括速度显示、运动数据、录制控制等
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#ifndef __DIALPLATE_C_H
#define __DIALPLATE_C_H

#include "page_manager_c.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 前向声明 */
typedef struct dialplate_c dialplate_c_t;
typedef struct dialplate_model_c dialplate_model_c_t;
typedef struct dialplate_view_c dialplate_view_c_t;

/* ========== 表盘录制状态枚举 ========== */

/**
 * @brief 录制状态枚举
 * 
 * 定义了运动录制的各种状态
 */
typedef enum {
    DIALPLATE_RECORD_STATE_READY,   // 准备状态，可以开始录制
    DIALPLATE_RECORD_STATE_RUN,     // 运行状态，正在录制
    DIALPLATE_RECORD_STATE_PAUSE,   // 暂停状态，录制已暂停
    DIALPLATE_RECORD_STATE_STOP     // 停止状态，准备结束录制
} dialplate_record_state_t;

/**
 * @brief 录制命令枚举
 * 
 * 定义了录制控制命令
 */
typedef enum {
    DIALPLATE_REC_START,        // 开始录制
    DIALPLATE_REC_PAUSE,        // 暂停录制
    DIALPLATE_REC_CONTINUE,     // 继续录制
    DIALPLATE_REC_STOP,         // 停止录制
    DIALPLATE_REC_READY_STOP    // 准备停止录制
} dialplate_rec_cmd_t;

/* ========== 运动状态信息结构 ========== */

/**
 * @brief 运动状态信息结构
 * 
 * 包含运动过程中的各种数据
 */
typedef struct {
    float speed_kph;            // 当前速度 (km/h)
    float speed_avg_kph;        // 平均速度 (km/h)
    uint32_t single_time;       // 单次运动时间 (秒)
    uint32_t single_distance;   // 单次运动距离 (米)
    uint32_t single_calorie;    // 单次运动卡路里
    uint32_t total_time;        // 总运动时间 (秒)
    uint32_t total_distance;    // 总运动距离 (米)
    uint32_t total_calorie;     // 总运动卡路里
} dialplate_sport_status_t;

/**
 * @brief GPS信息结构
 * 
 * 包含GPS相关信息
 */
typedef struct {
    int satellites;             // 卫星数量
    bool is_valid;              // GPS信号是否有效
    double latitude;            // 纬度
    double longitude;           // 经度
    float altitude;             // 海拔高度
} dialplate_gps_info_t;

/* ========== 视图UI结构 ========== */

/**
 * @brief 子信息显示结构
 * 
 * 用于显示运动数据的子项
 */
typedef struct {
    lv_obj_t* cont;             // 容器对象
    lv_obj_t* label_value;      // 数值标签
    lv_obj_t* label_unit;       // 单位标签
} dialplate_sub_info_t;

/**
 * @brief 表盘视图UI结构
 * 
 * 包含表盘界面的所有UI元素
 */
typedef struct {
    /* 顶部信息区域 */
    struct {
        lv_obj_t* cont;             // 顶部容器
        lv_obj_t* label_speed;      // 速度标签
        lv_obj_t* label_unit;       // 速度单位标签
    } top_info;
    
    /* 底部信息区域 */
    struct {
        lv_obj_t* cont;             // 底部容器
        dialplate_sub_info_t label_info_grp[4];  // 信息组数组
    } bottom_info;
    
    /* 按钮控制区域 */
    struct {
        lv_obj_t* cont;             // 按钮容器
        lv_obj_t* btn_map;          // 地图按钮
        lv_obj_t* btn_rec;          // 录制按钮
        lv_obj_t* btn_menu;         // 菜单按钮
    } btn_cont;
    
    lv_anim_timeline_t* anim_timeline;  // 动画时间线
} dialplate_view_ui_t;

/* ========== 表盘模型结构 ========== */

/**
 * @brief 表盘模型结构
 * 
 * 处理数据逻辑和业务逻辑
 */
struct dialplate_model_c {
    dialplate_sport_status_t sport_status_info;    // 运动状态信息
    dialplate_gps_info_t gps_info;                 // GPS信息
    void* account;                                  // 数据账户指针（模拟Account系统）
    void* user_data;                                // 用户数据
};

/* ========== 表盘视图结构 ========== */

/**
 * @brief 表盘视图结构
 * 
 * 处理UI渲染和显示
 */
struct dialplate_view_c {
    dialplate_view_ui_t ui;     // UI元素结构
    void* user_data;            // 用户数据
};

/* ========== 表盘主结构 ========== */

/**
 * @brief 表盘主结构
 * 
 * 继承页面基类，实现MVP模式的表盘页面
 */
struct dialplate_c {
    pm_page_base_t base;                    // 页面基类（必须是第一个成员）
    
    dialplate_view_c_t view;                // 视图组件
    dialplate_model_c_t model;              // 模型组件
    
    lv_timer_t* timer;                      // 更新定时器
    dialplate_record_state_t rec_state;     // 录制状态
    lv_obj_t* last_focus;                   // 最后焦点对象
};

/* ========== 表盘模型函数声明 ========== */

/* 模型生命周期 */
dialplate_model_c_t* dialplate_model_create(void);
void dialplate_model_destroy(dialplate_model_c_t* model);
void dialplate_model_init(dialplate_model_c_t* model);
void dialplate_model_deinit(dialplate_model_c_t* model);

/* 数据获取 */
bool dialplate_model_get_gps_ready(dialplate_model_c_t* model);
float dialplate_model_get_speed(dialplate_model_c_t* model);
float dialplate_model_get_avg_speed(dialplate_model_c_t* model);

/* 控制命令 */
void dialplate_model_recorder_command(dialplate_model_c_t* model, dialplate_rec_cmd_t cmd);
void dialplate_model_play_music(dialplate_model_c_t* model, const char* music);
void dialplate_model_set_status_bar_style(dialplate_model_c_t* model, int style);

/* 数据更新 */
void dialplate_model_update_sport_status(dialplate_model_c_t* model, const dialplate_sport_status_t* status);
void dialplate_model_update_gps_info(dialplate_model_c_t* model, const dialplate_gps_info_t* gps);

/* ========== 表盘视图函数声明 ========== */

/* 视图生命周期 */
dialplate_view_c_t* dialplate_view_create(void);
void dialplate_view_destroy(dialplate_view_c_t* view);
void dialplate_view_create_ui(dialplate_view_c_t* view, lv_obj_t* root);
void dialplate_view_delete_ui(dialplate_view_c_t* view);

/* 动画控制 */
void dialplate_view_appear_anim_start(dialplate_view_c_t* view, bool reverse);

/* UI更新 */
void dialplate_view_update_speed(dialplate_view_c_t* view, int speed);
void dialplate_view_update_info(dialplate_view_c_t* view, int index, const char* value);
void dialplate_view_set_btn_rec_img_src(dialplate_view_c_t* view, const char* src_name);

/* ========== 表盘主类函数声明 ========== */

/* 表盘生命周期 */
dialplate_c_t* dialplate_create(void);
void dialplate_destroy(dialplate_c_t* dialplate);

/* 页面生命周期回调（实现PageBase虚函数） */
void dialplate_on_custom_attr_config(pm_page_base_t* self);
void dialplate_on_view_load(pm_page_base_t* self);
void dialplate_on_view_did_load(pm_page_base_t* self);
void dialplate_on_view_will_appear(pm_page_base_t* self);
void dialplate_on_view_did_appear(pm_page_base_t* self);
void dialplate_on_view_will_disappear(pm_page_base_t* self);
void dialplate_on_view_did_disappear(pm_page_base_t* self);
void dialplate_on_view_unload(pm_page_base_t* self);
void dialplate_on_view_did_unload(pm_page_base_t* self);

/* 内部功能函数 */
void dialplate_update(dialplate_c_t* dialplate);
void dialplate_attach_event(dialplate_c_t* dialplate, lv_obj_t* obj);
void dialplate_on_btn_clicked(dialplate_c_t* dialplate, lv_obj_t* btn);
void dialplate_on_record(dialplate_c_t* dialplate, bool long_press);

/* 静态回调函数 */
void dialplate_on_timer_update(lv_timer_t* timer);
void dialplate_on_event(lv_event_t* event);

/* ========== 工具函数 ========== */

/* 时间格式化 */
const char* dialplate_make_time_string(uint32_t time_sec, char* buf, size_t buf_size);

/* 资源获取（需要根据实际资源系统实现） */
const void* dialplate_get_image(const char* name);
const lv_font_t* dialplate_get_font(const char* name);

#ifdef __cplusplus
}
#endif

#endif /* __DIALPLATE_C_H */
