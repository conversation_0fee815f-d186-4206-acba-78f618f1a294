/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Dialplate View Implementation
 */
#include "dialplate_c.h"
#include "pm_log_c.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* 内部函数声明 */
static void dialplate_view_top_info_create(dialplate_view_c_t* view, lv_obj_t* par);
static void dialplate_view_bottom_info_create(dialplate_view_c_t* view, lv_obj_t* par);
static void dialplate_view_sub_info_grp_create(lv_obj_t* par, dialplate_sub_info_t* info, const char* unit_text);
static void dialplate_view_btn_cont_create(dialplate_view_c_t* view, lv_obj_t* par);
static lv_obj_t* dialplate_view_btn_create(lv_obj_t* par, const void* img_src, lv_coord_t x_ofs);

/* ========== 表盘视图实现 ========== */

/**
 * @brief 创建表盘视图对象
 * @retval 视图对象指针，失败返回NULL
 */
dialplate_view_c_t* dialplate_view_create(void)
{
    dialplate_view_c_t* view = (dialplate_view_c_t*)malloc(sizeof(dialplate_view_c_t));
    if (view == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for dialplate view");
        return NULL;
    }
    
    /* 初始化成员变量 */
    memset(view, 0, sizeof(dialplate_view_c_t));
    
    PM_LOG_INFO("Dialplate view created");
    return view;
}

/**
 * @brief 销毁表盘视图对象
 * @param view 视图对象指针
 */
void dialplate_view_destroy(dialplate_view_c_t* view)
{
    if (view == NULL) {
        return;
    }
    
    /* 删除UI */
    dialplate_view_delete_ui(view);
    
    free(view);
    PM_LOG_INFO("Dialplate view destroyed");
}

/**
 * @brief 创建表盘视图UI
 * @param view 视图对象指针
 * @param root 根对象
 */
void dialplate_view_create_ui(dialplate_view_c_t* view, lv_obj_t* root)
{
    if (view == NULL || root == NULL) {
        return;
    }
    
    /* 创建各个UI组件 */
    dialplate_view_bottom_info_create(view, root);
    dialplate_view_top_info_create(view, root);
    dialplate_view_btn_cont_create(view, root);
    
    /* 创建动画时间线 */
    view->ui.anim_timeline = lv_anim_timeline_create();
    
    /* 定义动画宏 */
    #define ANIM_DEF(start_time, obj, attr, start, end) \
        {start_time, obj, LV_ANIM_EXEC(attr), start, end, 500, lv_anim_path_ease_out, true}
    
    #define ANIM_OPA_DEF(start_time, obj) \
        ANIM_DEF(start_time, obj, opa_scale, LV_OPA_TRANSP, LV_OPA_COVER)
    
    /* 获取目标位置 */
    lv_coord_t y_tar_top = lv_obj_get_y(view->ui.top_info.cont);
    lv_coord_t y_tar_bottom = lv_obj_get_y(view->ui.bottom_info.cont);
    lv_coord_t h_tar_btn = lv_obj_get_height(view->ui.btn_cont.btn_rec);
    
    /* 定义动画序列 */
    lv_anim_timeline_wrapper_t wrapper[] = {
        /* 顶部信息从上方滑入 */
        ANIM_DEF(0, view->ui.top_info.cont, y, -lv_obj_get_height(view->ui.top_info.cont), y_tar_top),
        
        /* 底部信息从下方滑入并淡入 */
        ANIM_DEF(200, view->ui.bottom_info.cont, y, -lv_obj_get_height(view->ui.bottom_info.cont), y_tar_bottom),
        ANIM_OPA_DEF(200, view->ui.bottom_info.cont),
        
        /* 按钮依次展开 */
        ANIM_DEF(500, view->ui.btn_cont.btn_map, height, 0, h_tar_btn),
        ANIM_DEF(600, view->ui.btn_cont.btn_rec, height, 0, h_tar_btn),
        ANIM_DEF(700, view->ui.btn_cont.btn_menu, height, 0, h_tar_btn),
        LV_ANIM_TIMELINE_WRAPPER_END
    };
    
    /* 添加动画到时间线 */
    lv_anim_timeline_add_wrapper(view->ui.anim_timeline, wrapper);
    
    PM_LOG_INFO("Dialplate view UI created");
}

/**
 * @brief 删除表盘视图UI
 * @param view 视图对象指针
 */
void dialplate_view_delete_ui(dialplate_view_c_t* view)
{
    if (view == NULL) {
        return;
    }
    
    /* 删除动画时间线 */
    if (view->ui.anim_timeline) {
        lv_anim_timeline_del(view->ui.anim_timeline);
        view->ui.anim_timeline = NULL;
    }
    
    /* UI对象会随着根对象一起被删除，这里只需要清空指针 */
    memset(&view->ui, 0, sizeof(dialplate_view_ui_t));
    
    PM_LOG_INFO("Dialplate view UI deleted");
}

/* ========== 动画控制函数 ========== */

/**
 * @brief 启动显示动画
 * @param view 视图对象指针
 * @param reverse 是否反向播放
 */
void dialplate_view_appear_anim_start(dialplate_view_c_t* view, bool reverse)
{
    if (view == NULL || view->ui.anim_timeline == NULL) {
        return;
    }
    
    lv_anim_timeline_set_reverse(view->ui.anim_timeline, reverse);
    lv_anim_timeline_start(view->ui.anim_timeline);
    
    PM_LOG_INFO("Dialplate appear animation started (reverse: %s)", reverse ? "true" : "false");
}

/* ========== UI更新函数 ========== */

/**
 * @brief 更新速度显示
 * @param view 视图对象指针
 * @param speed 速度值
 */
void dialplate_view_update_speed(dialplate_view_c_t* view, int speed)
{
    if (view == NULL || view->ui.top_info.label_speed == NULL) {
        return;
    }
    
    lv_label_set_text_fmt(view->ui.top_info.label_speed, "%02d", speed);
}

/**
 * @brief 更新信息显示
 * @param view 视图对象指针
 * @param index 信息索引（0-3）
 * @param value 显示值
 */
void dialplate_view_update_info(dialplate_view_c_t* view, int index, const char* value)
{
    if (view == NULL || value == NULL || index < 0 || index >= 4) {
        return;
    }
    
    if (view->ui.bottom_info.label_info_grp[index].label_value) {
        lv_label_set_text(view->ui.bottom_info.label_info_grp[index].label_value, value);
    }
}

/**
 * @brief 设置录制按钮图片
 * @param view 视图对象指针
 * @param src_name 图片源名称
 */
void dialplate_view_set_btn_rec_img_src(dialplate_view_c_t* view, const char* src_name)
{
    if (view == NULL || src_name == NULL || view->ui.btn_cont.btn_rec == NULL) {
        return;
    }
    
    const void* img_src = dialplate_get_image(src_name);
    lv_obj_set_style_bg_img_src(view->ui.btn_cont.btn_rec, img_src, 0);
    
    PM_LOG_INFO("Record button image set to: %s", src_name);
}

/* ========== 内部UI创建函数 ========== */

/**
 * @brief 创建顶部信息区域
 * @param view 视图对象指针
 * @param par 父对象
 */
static void dialplate_view_top_info_create(dialplate_view_c_t* view, lv_obj_t* par)
{
    if (view == NULL || par == NULL) {
        return;
    }
    
    /* 创建顶部容器 */
    lv_obj_t* cont = lv_obj_create(par);
    lv_obj_remove_style_all(cont);
    lv_obj_set_size(cont, LV_HOR_RES, 142);
    
    /* 设置容器样式 */
    lv_obj_set_style_bg_opa(cont, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(cont, lv_color_hex(0x333333), 0);
    lv_obj_set_style_radius(cont, 27, 0);
    lv_obj_set_y(cont, -36);
    
    view->ui.top_info.cont = cont;
    
    /* 创建速度标签 */
    lv_obj_t* label_speed = lv_label_create(cont);
    lv_obj_set_style_text_font(label_speed, dialplate_get_font("bahnschrift_65"), 0);
    lv_obj_set_style_text_color(label_speed, lv_color_white(), 0);
    lv_label_set_text(label_speed, "00");
    lv_obj_align(label_speed, LV_ALIGN_TOP_MID, 0, 63);
    view->ui.top_info.label_speed = label_speed;
    
    /* 创建单位标签 */
    lv_obj_t* label_unit = lv_label_create(cont);
    lv_obj_set_style_text_font(label_unit, dialplate_get_font("bahnschrift_17"), 0);
    lv_obj_set_style_text_color(label_unit, lv_color_white(), 0);
    lv_label_set_text(label_unit, "km/h");
    lv_obj_align_to(label_unit, label_speed, LV_ALIGN_OUT_BOTTOM_MID, 0, -5);
    view->ui.top_info.label_unit = label_unit;
    
    PM_LOG_INFO("Top info area created");
}

/**
 * @brief 创建底部信息区域
 * @param view 视图对象指针
 * @param par 父对象
 */
static void dialplate_view_bottom_info_create(dialplate_view_c_t* view, lv_obj_t* par)
{
    if (view == NULL || par == NULL) {
        return;
    }
    
    /* 创建底部容器 */
    lv_obj_t* cont = lv_obj_create(par);
    lv_obj_remove_style_all(cont);
    lv_obj_set_size(cont, LV_HOR_RES, 85);
    lv_obj_align(cont, LV_ALIGN_BOTTOM_MID, 0, -40);
    
    /* 设置弹性布局 */
    lv_obj_set_flex_flow(cont, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_place(cont, LV_FLEX_PLACE_SPACE_AROUND, LV_FLEX_PLACE_CENTER, LV_FLEX_PLACE_CENTER);
    
    view->ui.bottom_info.cont = cont;
    
    /* 创建信息组 */
    const char* unit_texts[] = {"AVG", "TIME", "DIST", "CAL"};
    
    for (int i = 0; i < 4; i++) {
        dialplate_view_sub_info_grp_create(cont, &view->ui.bottom_info.label_info_grp[i], unit_texts[i]);
    }
    
    PM_LOG_INFO("Bottom info area created");
}

/**
 * @brief 创建子信息组
 * @param par 父对象
 * @param info 信息结构指针
 * @param unit_text 单位文本
 */
static void dialplate_view_sub_info_grp_create(lv_obj_t* par, dialplate_sub_info_t* info, const char* unit_text)
{
    if (par == NULL || info == NULL || unit_text == NULL) {
        return;
    }
    
    /* 创建容器 */
    lv_obj_t* cont = lv_obj_create(par);
    lv_obj_remove_style_all(cont);
    lv_obj_set_size(cont, 55, 85);
    info->cont = cont;
    
    /* 创建数值标签 */
    lv_obj_t* label_value = lv_label_create(cont);
    lv_obj_set_style_text_font(label_value, dialplate_get_font("bahnschrift_17"), 0);
    lv_obj_set_style_text_color(label_value, lv_color_white(), 0);
    lv_label_set_text(label_value, "0");
    lv_obj_align(label_value, LV_ALIGN_TOP_MID, 0, 20);
    info->label_value = label_value;
    
    /* 创建单位标签 */
    lv_obj_t* label_unit = lv_label_create(cont);
    lv_obj_set_style_text_font(label_unit, dialplate_get_font("bahnschrift_13"), 0);
    lv_obj_set_style_text_color(label_unit, lv_color_hex(0x999999), 0);
    lv_label_set_text(label_unit, unit_text);
    lv_obj_align_to(label_unit, label_value, LV_ALIGN_OUT_BOTTOM_MID, 0, 5);
    info->label_unit = label_unit;
}

/**
 * @brief 创建按钮控制区域
 * @param view 视图对象指针
 * @param par 父对象
 */
static void dialplate_view_btn_cont_create(dialplate_view_c_t* view, lv_obj_t* par)
{
    if (view == NULL || par == NULL) {
        return;
    }

    /* 创建按钮容器 */
    lv_obj_t* cont = lv_obj_create(par);
    lv_obj_remove_style_all(cont);
    lv_obj_set_size(cont, 194, 58);
    lv_obj_align(cont, LV_ALIGN_CENTER, 0, 60);

    /* 设置弹性布局 */
    lv_obj_set_flex_flow(cont, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_place(cont, LV_FLEX_PLACE_SPACE_BETWEEN, LV_FLEX_PLACE_CENTER, LV_FLEX_PLACE_CENTER);

    view->ui.btn_cont.cont = cont;

    /* 创建地图按钮 */
    view->ui.btn_cont.btn_map = dialplate_view_btn_create(cont, dialplate_get_image("map"), -58);

    /* 创建录制按钮 */
    view->ui.btn_cont.btn_rec = dialplate_view_btn_create(cont, dialplate_get_image("start"), 0);

    /* 创建菜单按钮 */
    view->ui.btn_cont.btn_menu = dialplate_view_btn_create(cont, dialplate_get_image("menu"), 58);

    PM_LOG_INFO("Button control area created");
}

/**
 * @brief 创建按钮
 * @param par 父对象
 * @param img_src 图片源
 * @param x_ofs X轴偏移
 * @retval 创建的按钮对象
 */
static lv_obj_t* dialplate_view_btn_create(lv_obj_t* par, const void* img_src, lv_coord_t x_ofs)
{
    if (par == NULL) {
        return NULL;
    }

    /* 创建按钮对象 */
    lv_obj_t* btn = lv_obj_create(par);
    lv_obj_remove_style_all(btn);
    lv_obj_set_size(btn, 58, 58);

    /* 设置按钮样式 */
    lv_obj_set_style_bg_opa(btn, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(btn, lv_color_hex(0x333333), 0);
    lv_obj_set_style_bg_img_src(btn, img_src, 0);
    lv_obj_set_style_radius(btn, 29, 0);

    /* 设置按下状态样式 */
    lv_obj_set_style_bg_color(btn, lv_color_hex(0x555555), LV_STATE_PRESSED);
    lv_obj_set_style_transform_zoom(btn, 240, LV_STATE_PRESSED);

    /* 设置焦点状态样式 */
    lv_obj_set_style_outline_width(btn, 2, LV_STATE_FOCUSED);
    lv_obj_set_style_outline_color(btn, lv_color_white(), LV_STATE_FOCUSED);
    lv_obj_set_style_outline_opa(btn, LV_OPA_50, LV_STATE_FOCUSED);

    /* 添加可点击标志 */
    lv_obj_add_flag(btn, LV_OBJ_FLAG_CLICKABLE);

    /* 设置位置 */
    lv_obj_align(btn, LV_ALIGN_CENTER, x_ofs, 0);

    return btn;
}

/* ========== 样式设置函数 ========== */

/**
 * @brief 设置按钮样式
 * @param btn 按钮对象
 * @param style_type 样式类型（0=正常，1=高亮，2=禁用）
 */
void dialplate_view_set_btn_style(lv_obj_t* btn, int style_type)
{
    if (btn == NULL) {
        return;
    }

    switch (style_type) {
    case 0: /* 正常样式 */
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x333333), 0);
        lv_obj_set_style_bg_opa(btn, LV_OPA_COVER, 0);
        break;

    case 1: /* 高亮样式 */
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x00AA00), 0);
        lv_obj_set_style_bg_opa(btn, LV_OPA_COVER, 0);
        break;

    case 2: /* 禁用样式 */
        lv_obj_set_style_bg_color(btn, lv_color_hex(0x666666), 0);
        lv_obj_set_style_bg_opa(btn, LV_OPA_50, 0);
        break;

    default:
        break;
    }
}

/**
 * @brief 设置信息标签颜色
 * @param view 视图对象指针
 * @param index 信息索引
 * @param color 颜色值
 */
void dialplate_view_set_info_color(dialplate_view_c_t* view, int index, lv_color_t color)
{
    if (view == NULL || index < 0 || index >= 4) {
        return;
    }

    if (view->ui.bottom_info.label_info_grp[index].label_value) {
        lv_obj_set_style_text_color(view->ui.bottom_info.label_info_grp[index].label_value, color, 0);
    }
}

/**
 * @brief 设置速度标签颜色
 * @param view 视图对象指针
 * @param color 颜色值
 */
void dialplate_view_set_speed_color(dialplate_view_c_t* view, lv_color_t color)
{
    if (view == NULL || view->ui.top_info.label_speed == NULL) {
        return;
    }

    lv_obj_set_style_text_color(view->ui.top_info.label_speed, color, 0);
}

/* ========== 动画辅助函数 ========== */

/**
 * @brief 设置UI元素的初始动画状态
 * @param view 视图对象指针
 */
void dialplate_view_set_initial_anim_state(dialplate_view_c_t* view)
{
    if (view == NULL) {
        return;
    }

    /* 设置顶部信息初始位置 */
    if (view->ui.top_info.cont) {
        lv_obj_set_y(view->ui.top_info.cont, -lv_obj_get_height(view->ui.top_info.cont));
    }

    /* 设置底部信息初始状态 */
    if (view->ui.bottom_info.cont) {
        lv_obj_set_y(view->ui.bottom_info.cont, -lv_obj_get_height(view->ui.bottom_info.cont));
        lv_obj_set_style_opa(view->ui.bottom_info.cont, LV_OPA_TRANSP, 0);
    }

    /* 设置按钮初始高度 */
    if (view->ui.btn_cont.btn_map) {
        lv_obj_set_height(view->ui.btn_cont.btn_map, 0);
    }
    if (view->ui.btn_cont.btn_rec) {
        lv_obj_set_height(view->ui.btn_cont.btn_rec, 0);
    }
    if (view->ui.btn_cont.btn_menu) {
        lv_obj_set_height(view->ui.btn_cont.btn_menu, 0);
    }
}

/**
 * @brief 获取视图UI结构指针（用于外部访问）
 * @param view 视图对象指针
 * @retval UI结构指针
 */
dialplate_view_ui_t* dialplate_view_get_ui(dialplate_view_c_t* view)
{
    if (view == NULL) {
        return NULL;
    }

    return &view->ui;
}
