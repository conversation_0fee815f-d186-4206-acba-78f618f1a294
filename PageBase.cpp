/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#include "PageBase.h"
#include "PM_Log.h"

/**
 * @brief  设置自定义缓存启用状态
 * @param  en: 是否启用缓存
 * @retval None
 */
void PageBase::SetCustomCacheEnable(bool en)
{
    PM_LOG_INFO("Page(%s) %s = %d", _Name, __func__, en);
    /* 禁用自动缓存管理 */
    SetCustomAutoCacheEnable(false);
    /* 设置手动缓存请求 */
    priv.ReqEnableCache = en;
}

/**
 * @brief  设置自动缓存启用状态
 * @param  en: 是否启用自动缓存
 * @retval None
 */
void PageBase::SetCustomAutoCacheEnable(bool en)
{
    PM_LOG_INFO("Page(%s) %s = %d", _Name, __func__, en);
    /* 设置是否禁用自动缓存（取反逻辑） */
    priv.ReqDisableAutoCache = !en;
}

/**
 * @brief  设置自定义加载动画类型
 * @param  animType: 动画类型
 * @param  time: 动画持续时间（毫秒）
 * @param  path: 动画路径/缓动函数
 * @retval None
 */
void PageBase::SetCustomLoadAnimType(
    uint8_t animType,
    uint16_t time,
    lv_anim_path_cb_t path
)
{
    /* 设置动画类型 */
    priv.Anim.Attr.Type = animType;
    /* 设置动画持续时间 */
    priv.Anim.Attr.Time = time;
    /* 设置动画路径函数 */
    priv.Anim.Attr.Path = path;
}

/**
 * @brief  从页面参数缓存区弹出数据
 * @param  ptr: 目标缓冲区指针
 * @param  size: 期望的数据大小
 * @retval 成功返回true，失败返回false
 */
bool PageBase::StashPop(void* ptr, uint32_t size)
{
    /* 检查缓存区是否存在 */
    if (priv.Stash.ptr == nullptr)
    {
        PM_LOG_WARN("No Stash found");
        return false;
    }

    /* 检查数据大小是否匹配 */
    if (priv.Stash.size != size)
    {
        PM_LOG_WARN(
            "Stash[0x%p](%d) does not match the size(%d)",
            priv.Stash.ptr,
            priv.Stash.size,
            size
        );
        return false;
    }

    /* 复制数据到目标缓冲区 */
    memcpy(ptr, priv.Stash.ptr, priv.Stash.size);
    /* 释放缓存区内存 */
    lv_mem_free(priv.Stash.ptr);
    /* 清空缓存区指针 */
    priv.Stash.ptr = nullptr;
    return true;
}

