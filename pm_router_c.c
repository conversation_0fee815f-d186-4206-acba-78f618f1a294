/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Router Implementation
 */
#include "page_manager_c.h"
#include "pm_log_c.h"

/* 前向声明 */
static bool pm_page_manager_switch_to(pm_page_manager_t* manager, pm_page_base_t* new_page, bool is_enter_act, const pm_stash_t* stash);
static bool pm_page_manager_switch_anim_state_check(pm_page_manager_t* manager);

/**
 * @brief  进入新页面，替换旧页面
 * @param  manager: 页面管理器指针
 * @param  name: 要进入的页面名称
 * @param  stash: 传递给新页面的参数
 * @retval 成功返回true
 */
bool pm_page_manager_replace(pm_page_manager_t* manager, const char* name, const pm_stash_t* stash)
{
    if (manager == NULL || name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }
    
    /* 检查页面切换动画是否正在执行 */
    if (!pm_page_manager_switch_anim_state_check(manager)) {
        return false;
    }
    
    /* 检查堆栈中是否重复推入页面 */
    if (pm_page_manager_find_page_in_stack(manager, name) != NULL) {
        PM_LOG_ERROR("Page(%s) was multi push", name);
        return false;
    }
    
    /* 检查页面是否已在页面池中注册 */
    pm_page_base_t* base = pm_page_manager_find_page_in_pool(manager, name);
    if (base == NULL) {
        PM_LOG_ERROR("Page(%s) was not install", name);
        return false;
    }
    
    /* 获取堆栈顶部页面 */
    pm_page_base_t* top = pm_page_manager_get_stack_top(manager);
    if (top == NULL) {
        PM_LOG_ERROR("Stack top is NULL");
        return false;
    }
    
    /* 同步自动缓存配置 */
    base->priv.is_disable_auto_cache = base->priv.req_disable_auto_cache;
    
    /* 替换堆栈顶部页面 */
    manager->page_stack[manager->stack_top - 1] = base;
    
    PM_LOG_INFO("Page(%s) replace >> [Screen] (stash = 0x%p)", name, stash);
    
    /* 执行页面切换 */
    return pm_page_manager_switch_to(manager, base, true, stash);
}

/**
 * @brief  进入新页面，旧页面被推入堆栈
 * @param  manager: 页面管理器指针
 * @param  name: 要进入的页面名称
 * @param  stash: 传递给新页面的参数
 * @retval 成功返回true
 */
bool pm_page_manager_push(pm_page_manager_t* manager, const char* name, const pm_stash_t* stash)
{
    if (manager == NULL || name == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }
    
    /* 检查页面切换动画是否正在执行 */
    if (!pm_page_manager_switch_anim_state_check(manager)) {
        return false;
    }
    
    /* 检查堆栈中是否重复推入页面 */
    if (pm_page_manager_find_page_in_stack(manager, name) != NULL) {
        PM_LOG_ERROR("Page(%s) was multi push", name);
        return false;
    }
    
    /* 检查页面是否已在页面池中注册 */
    pm_page_base_t* base = pm_page_manager_find_page_in_pool(manager, name);
    if (base == NULL) {
        PM_LOG_ERROR("Page(%s) was not install", name);
        return false;
    }
    
    /* 检查堆栈是否已满 */
    if (manager->stack_top >= PM_MAX_PAGE_STACK_SIZE) {
        PM_LOG_ERROR("Page stack is full");
        return false;
    }
    
    /* 同步自动缓存配置 */
    base->priv.is_disable_auto_cache = base->priv.req_disable_auto_cache;
    
    /* 推入堆栈 */
    manager->page_stack[manager->stack_top] = base;
    manager->stack_top++;
    
    PM_LOG_INFO("Page(%s) push >> [Screen] (stash = 0x%p)", name, stash);
    
    /* 执行页面切换 */
    return pm_page_manager_switch_to(manager, base, true, stash);
}

/**
 * @brief  弹出当前页面
 * @param  manager: 页面管理器指针
 * @retval 成功返回true
 */
bool pm_page_manager_pop(pm_page_manager_t* manager)
{
    if (manager == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }
    
    /* 检查页面切换动画是否正在执行 */
    if (!pm_page_manager_switch_anim_state_check(manager)) {
        return false;
    }
    
    /* 获取堆栈顶部页面 */
    pm_page_base_t* top = pm_page_manager_get_stack_top(manager);
    if (top == NULL) {
        PM_LOG_WARN("Page stack is empty, can't pop");
        return false;
    }
    
    /* 是否关闭自动缓存 */
    if (!top->priv.is_disable_auto_cache) {
        PM_LOG_INFO("Page(%s) has auto cache, cache disabled", top->name);
        top->priv.is_cached = false;
    }
    
    PM_LOG_INFO("Page(%s) pop << [Screen]", top->name);
    
    /* 页面弹出 */
    manager->stack_top--;
    
    /* 获取下一个页面 */
    top = pm_page_manager_get_stack_top(manager);
    
    /* 执行页面切换 */
    return pm_page_manager_switch_to(manager, top, false, NULL);
}

/**
 * @brief  返回主页面（堆栈底部的页面）
 * @param  manager: 页面管理器指针
 * @retval 成功返回true
 */
bool pm_page_manager_back_home(pm_page_manager_t* manager)
{
    if (manager == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }
    
    /* 检查页面切换动画是否正在执行 */
    if (!pm_page_manager_switch_anim_state_check(manager)) {
        return false;
    }
    
    pm_page_manager_set_stack_clear(manager, true);
    
    manager->page_prev = NULL;
    
    pm_page_base_t* home = pm_page_manager_get_stack_top(manager);
    
    return pm_page_manager_switch_to(manager, home, false, NULL);
}

/**
 * @brief  检查页面切换动画是否正在执行
 * @param  manager: 页面管理器指针
 * @retval 正在执行返回false，可以切换返回true
 */
static bool pm_page_manager_switch_anim_state_check(pm_page_manager_t* manager)
{
    if (manager == NULL) {
        return false;
    }
    
    if (manager->anim_state.is_switch_req || manager->anim_state.is_busy) {
        PM_LOG_WARN(
            "Page switch busy[AnimState.IsSwitchReq = %d,"
            "AnimState.IsBusy = %d],"
            "request ignored",
            manager->anim_state.is_switch_req,
            manager->anim_state.is_busy
        );
        return false;
    }
    
    return true;
}

/**
 * @brief  页面切换
 * @param  manager: 页面管理器指针
 * @param  new_page: 新页面指针
 * @param  is_enter_act: 是否为进入动作
 * @param  stash: 传递给新页面的参数
 * @retval 成功返回true
 */
static bool pm_page_manager_switch_to(pm_page_manager_t* manager, pm_page_base_t* new_page, bool is_enter_act, const pm_stash_t* stash)
{
    if (manager == NULL || new_page == NULL) {
        PM_LOG_ERROR("Invalid parameters");
        return false;
    }
    
    /* 是否已请求页面切换 */
    if (manager->anim_state.is_switch_req) {
        PM_LOG_WARN("Page switch busy, require(%s) is ignore", new_page->name);
        return false;
    }
    
    /* 设置切换请求标志 */
    manager->anim_state.is_switch_req = true;
    manager->anim_state.is_entering = is_enter_act;
    
    /* 保存前一页面 */
    manager->page_prev = manager->page_current;
    manager->page_current = new_page;
    
    /* 设置stash数据 */
    if (stash != NULL && stash->ptr != NULL && stash->size > 0) {
        /* 释放旧的stash数据 */
        if (new_page->priv.stash.ptr != NULL) {
            lv_mem_free(new_page->priv.stash.ptr);
        }
        
        /* 分配新的stash数据 */
        new_page->priv.stash.ptr = lv_mem_alloc(stash->size);
        if (new_page->priv.stash.ptr != NULL) {
            memcpy(new_page->priv.stash.ptr, stash->ptr, stash->size);
            new_page->priv.stash.size = stash->size;
            PM_LOG_INFO("Page(%s) stash(0x%p)[%d] copy", new_page->name, new_page->priv.stash.ptr, stash->size);
        } else {
            PM_LOG_ERROR("Failed to allocate stash memory");
            new_page->priv.stash.size = 0;
        }
    }
    
    /* 设置页面动画属性 */
    pm_page_manager_switch_anim_type_update(manager, new_page);
    
    /* 更新前一页面的状态机 */
    if (manager->page_prev != NULL) {
        pm_page_manager_state_update(manager, manager->page_prev);
    }
    
    /* 更新当前页面的状态机 */
    pm_page_manager_state_update(manager, manager->page_current);
    
    /* 移动图层，将新页面移到前台 */
    if (manager->anim_state.is_entering) {
        PM_LOG_INFO("Page ENTER is detect, move Page(%s) to foreground", manager->page_current->name);
        if (manager->page_prev) {
            lv_obj_move_foreground(manager->page_prev->root);
        }
        lv_obj_move_foreground(manager->page_current->root);
    } else {
        PM_LOG_INFO("Page EXIT is detect, move Page(%s) to foreground", pm_page_manager_get_page_prev_name(manager));
        lv_obj_move_foreground(manager->page_current->root);
        if (manager->page_prev) {
            lv_obj_move_foreground(manager->page_prev->root);
        }
    }
    
    return true;
}
