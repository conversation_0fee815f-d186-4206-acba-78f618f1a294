/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Dialplate Model Implementation
 */
#include "dialplate_c.h"
#include "pm_log_c.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* ========== 表盘模型实现 ========== */

/**
 * @brief 创建表盘模型对象
 * @retval 模型对象指针，失败返回NULL
 */
dialplate_model_c_t* dialplate_model_create(void)
{
    dialplate_model_c_t* model = (dialplate_model_c_t*)malloc(sizeof(dialplate_model_c_t));
    if (model == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for dialplate model");
        return NULL;
    }
    
    /* 初始化成员变量 */
    memset(model, 0, sizeof(dialplate_model_c_t));
    
    PM_LOG_INFO("Dialplate model created");
    return model;
}

/**
 * @brief 销毁表盘模型对象
 * @param model 模型对象指针
 */
void dialplate_model_destroy(dialplate_model_c_t* model)
{
    if (model == NULL) {
        return;
    }
    
    /* 反初始化 */
    dialplate_model_deinit(model);
    
    free(model);
    PM_LOG_INFO("Dialplate model destroyed");
}

/**
 * @brief 初始化表盘模型
 * @param model 模型对象指针
 */
void dialplate_model_init(dialplate_model_c_t* model)
{
    if (model == NULL) {
        return;
    }
    
    /* 初始化运动状态信息 */
    memset(&model->sport_status_info, 0, sizeof(dialplate_sport_status_t));
    
    /* 初始化GPS信息 */
    memset(&model->gps_info, 0, sizeof(dialplate_gps_info_t));
    
    /* 这里应该初始化数据订阅系统，类似原版的Account系统 */
    /* 由于没有完整的DataProc系统，这里用简化实现 */
    model->account = NULL; // 实际使用时需要创建数据账户
    
    PM_LOG_INFO("Dialplate model initialized");
}

/**
 * @brief 反初始化表盘模型
 * @param model 模型对象指针
 */
void dialplate_model_deinit(dialplate_model_c_t* model)
{
    if (model == NULL) {
        return;
    }
    
    /* 清理数据订阅 */
    if (model->account) {
        /* 这里应该清理Account相关资源 */
        model->account = NULL;
    }
    
    PM_LOG_INFO("Dialplate model deinitialized");
}

/* ========== 数据获取函数 ========== */

/**
 * @brief 获取GPS是否准备好
 * @param model 模型对象指针
 * @retval true：GPS准备好，false：GPS未准备好
 */
bool dialplate_model_get_gps_ready(dialplate_model_c_t* model)
{
    if (model == NULL) {
        return false;
    }
    
    /* 检查GPS卫星数量 */
    return (model->gps_info.satellites > 0 && model->gps_info.is_valid);
}

/**
 * @brief 获取当前速度
 * @param model 模型对象指针
 * @retval 当前速度 (km/h)
 */
float dialplate_model_get_speed(dialplate_model_c_t* model)
{
    if (model == NULL) {
        return 0.0f;
    }
    
    return model->sport_status_info.speed_kph;
}

/**
 * @brief 获取平均速度
 * @param model 模型对象指针
 * @retval 平均速度 (km/h)
 */
float dialplate_model_get_avg_speed(dialplate_model_c_t* model)
{
    if (model == NULL) {
        return 0.0f;
    }
    
    return model->sport_status_info.speed_avg_kph;
}

/* ========== 控制命令函数 ========== */

/**
 * @brief 发送录制命令
 * @param model 模型对象指针
 * @param cmd 录制命令
 */
void dialplate_model_recorder_command(dialplate_model_c_t* model, dialplate_rec_cmd_t cmd)
{
    if (model == NULL) {
        return;
    }
    
    /* 这里应该通过DataProc系统发送录制命令 */
    /* 简化实现，仅记录日志 */
    const char* cmd_names[] = {
        "START", "PAUSE", "CONTINUE", "STOP", "READY_STOP"
    };
    
    if (cmd < sizeof(cmd_names) / sizeof(cmd_names[0])) {
        PM_LOG_INFO("Recorder command: %s", cmd_names[cmd]);
    }
    
    /* 模拟发送录制命令到系统 */
    if (cmd != DIALPLATE_REC_READY_STOP) {
        /* 发送录制命令 */
        PM_LOG_INFO("Sending recorder command to system");
    }
    
    /* 更新状态栏显示 */
    const char* status_text = NULL;
    bool show_status = true;
    
    switch (cmd) {
    case DIALPLATE_REC_START:
    case DIALPLATE_REC_CONTINUE:
        status_text = "REC";
        break;
    case DIALPLATE_REC_PAUSE:
        status_text = "PAUSE";
        break;
    case DIALPLATE_REC_READY_STOP:
        status_text = "STOP";
        break;
    case DIALPLATE_REC_STOP:
        show_status = false;
        break;
    default:
        break;
    }
    
    if (show_status && status_text) {
        PM_LOG_INFO("Status bar: %s", status_text);
    } else if (!show_status) {
        PM_LOG_INFO("Status bar: hidden");
    }
}

/**
 * @brief 播放音乐
 * @param model 模型对象指针
 * @param music 音乐名称
 */
void dialplate_model_play_music(dialplate_model_c_t* model, const char* music)
{
    if (model == NULL || music == NULL) {
        return;
    }
    
    /* 这里应该通过DataProc系统播放音乐 */
    /* 简化实现，仅记录日志 */
    PM_LOG_INFO("Playing music: %s", music);
}

/**
 * @brief 设置状态栏样式
 * @param model 模型对象指针
 * @param style 状态栏样式
 */
void dialplate_model_set_status_bar_style(dialplate_model_c_t* model, int style)
{
    if (model == NULL) {
        return;
    }
    
    /* 这里应该通过DataProc系统设置状态栏样式 */
    /* 简化实现，仅记录日志 */
    const char* style_names[] = {"TRANSP", "NORMAL", "DARK"};
    const char* style_name = (style < 3) ? style_names[style] : "UNKNOWN";
    
    PM_LOG_INFO("Setting status bar style: %s", style_name);
}

/* ========== 数据更新函数 ========== */

/**
 * @brief 更新运动状态信息
 * @param model 模型对象指针
 * @param status 运动状态信息
 */
void dialplate_model_update_sport_status(dialplate_model_c_t* model, const dialplate_sport_status_t* status)
{
    if (model == NULL || status == NULL) {
        return;
    }
    
    /* 更新运动状态信息 */
    model->sport_status_info = *status;
    
    PM_LOG_INFO("Sport status updated: speed=%.1f km/h, distance=%.1f km", 
                status->speed_kph, status->single_distance / 1000.0f);
}

/**
 * @brief 更新GPS信息
 * @param model 模型对象指针
 * @param gps GPS信息
 */
void dialplate_model_update_gps_info(dialplate_model_c_t* model, const dialplate_gps_info_t* gps)
{
    if (model == NULL || gps == NULL) {
        return;
    }
    
    /* 更新GPS信息 */
    model->gps_info = *gps;
    
    PM_LOG_INFO("GPS info updated: satellites=%d, valid=%s", 
                gps->satellites, gps->is_valid ? "true" : "false");
}

/* ========== 模拟数据事件处理 ========== */

/**
 * @brief 模拟数据事件回调（替代原版的onEvent）
 * @param model 模型对象指针
 * @param event_type 事件类型
 * @param data 事件数据
 * @param size 数据大小
 * @retval 处理结果
 */
int dialplate_model_on_data_event(dialplate_model_c_t* model, const char* event_type, 
                                  const void* data, size_t size)
{
    if (model == NULL || event_type == NULL || data == NULL) {
        return -1; // 参数错误
    }
    
    if (strcmp(event_type, "SportStatus") == 0 && size == sizeof(dialplate_sport_status_t)) {
        /* 处理运动状态更新 */
        dialplate_model_update_sport_status(model, (const dialplate_sport_status_t*)data);
        return 0; // 成功
    } else if (strcmp(event_type, "GPS") == 0 && size == sizeof(dialplate_gps_info_t)) {
        /* 处理GPS信息更新 */
        dialplate_model_update_gps_info(model, (const dialplate_gps_info_t*)data);
        return 0; // 成功
    }
    
    PM_LOG_WARN("Unsupported event type: %s", event_type);
    return -2; // 不支持的事件类型
}

/* ========== 测试数据生成函数 ========== */

/**
 * @brief 生成测试运动数据（用于演示）
 * @param model 模型对象指针
 */
void dialplate_model_generate_test_data(dialplate_model_c_t* model)
{
    if (model == NULL) {
        return;
    }
    
    /* 生成模拟运动数据 */
    dialplate_sport_status_t test_status = {
        .speed_kph = 15.5f,
        .speed_avg_kph = 12.8f,
        .single_time = 1800,        // 30分钟
        .single_distance = 6400,    // 6.4公里
        .single_calorie = 320,      // 320卡路里
        .total_time = 7200,         // 2小时
        .total_distance = 25600,    // 25.6公里
        .total_calorie = 1280       // 1280卡路里
    };
    
    /* 生成模拟GPS数据 */
    dialplate_gps_info_t test_gps = {
        .satellites = 8,
        .is_valid = true,
        .latitude = 39.9042,        // 北京纬度
        .longitude = 116.4074,      // 北京经度
        .altitude = 43.5f           // 海拔
    };
    
    dialplate_model_update_sport_status(model, &test_status);
    dialplate_model_update_gps_info(model, &test_gps);
    
    PM_LOG_INFO("Test data generated");
}
