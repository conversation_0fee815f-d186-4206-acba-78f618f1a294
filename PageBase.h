/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 *
 * 页面基类头文件
 * 定义了页面管理框架中所有页面的基础类，包含页面生命周期、状态管理、动画配置等核心功能
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#ifndef __PAGE_BASE_H
#define __PAGE_BASE_H

#include "lvgl/lvgl.h"

/* 生成stash区域数据的宏，用于页面间数据传递 */
#define PAGE_STASH_MAKE(data) {&(data), sizeof(data)}

/* 从stash区域获取数据的宏，在页面中使用 */
#define PAGE_STASH_POP(data)  this->StashPop(&(data), sizeof(data))

/* 页面动画默认时间，单位：毫秒 */
#define PAGE_ANIM_TIME_DEFAULT 500 //[ms]

/* 页面动画默认路径函数，缓出效果 */
#define PAGE_ANIM_PATH_DEFAULT lv_anim_path_ease_out

/* 前向声明页面管理器类 */
class PageManager;

/**
 * @brief 页面基类
 *
 * 所有页面都应该继承此基类，提供了页面的基础功能：
 * - 页面生命周期管理
 * - 页面状态跟踪
 * - 动画配置
 * - 数据传递机制
 * - 缓存管理
 */
class PageBase
{
public:

    /**
     * @brief 页面状态枚举
     *
     * 定义了页面在生命周期中的各个状态，状态转换由页面管理器控制
     */
    typedef enum
    {
        PAGE_STATE_IDLE,            // 空闲状态，页面未加载
        PAGE_STATE_LOAD,            // 加载状态，正在创建页面UI
        PAGE_STATE_WILL_APPEAR,     // 即将显示状态，页面准备显示
        PAGE_STATE_DID_APPEAR,      // 已显示状态，页面显示动画完成
        PAGE_STATE_ACTIVITY,        // 活动状态，页面正在前台运行
        PAGE_STATE_WILL_DISAPPEAR,  // 即将消失状态，页面准备隐藏
        PAGE_STATE_DID_DISAPPEAR,   // 已消失状态，页面隐藏动画完成
        PAGE_STATE_UNLOAD,          // 卸载状态，正在销毁页面UI
        _PAGE_STATE_LAST            // 状态枚举结束标记
    } State_t;

    /**
     * @brief Stash数据区域结构
     *
     * 用于页面间数据传递的临时存储区域
     */
    typedef struct
    {
        void* ptr;          // 数据指针
        uint32_t size;      // 数据大小（字节）
    } Stash_t;

    /**
     * @brief 页面切换动画属性结构
     *
     * 定义页面切换时的动画参数
     */
    typedef struct
    {
        uint8_t Type;               // 动画类型
        uint16_t Time;              // 动画持续时间（毫秒）
        lv_anim_path_cb_t Path;     // 动画路径函数（缓动函数）
    } AnimAttr_t;

public:
    /**
     * @brief 页面公共成员变量
     *
     * 这些成员变量可以被页面管理器和页面本身访问
     */
    lv_obj_t* _root;       // UI根节点，页面的顶层容器对象
    PageManager* _Manager; // 页面管理器指针，指向管理此页面的管理器
    const char* _Name;     // 页面名称，用于页面识别和路由
    uint16_t _ID;          // 页面ID，页面的唯一标识符
    void* _UserData;       // 用户数据指针，可存储页面相关的自定义数据

    /**
     * @brief 页面私有数据结构
     *
     * 仅供页面管理器访问的内部数据，用户代码不应直接修改
     */
    struct
    {
        // 缓存管理相关
        bool ReqEnableCache;        // 缓存启用请求标志
        bool ReqDisableAutoCache;   // 禁用自动缓存管理请求标志

        bool IsDisableAutoCache;    // 是否禁用自动缓存管理
        bool IsCached;              // 页面是否已缓存

        // 数据传递相关
        Stash_t Stash;              // Stash数据区域，用于页面间数据传递
        State_t State;              // 当前页面状态

        /**
         * @brief 动画状态结构
         *
         * 记录页面动画的相关状态信息
         */
        struct
        {
            bool IsEnter;           // 是否为进入动画（true：进入，false：退出）
            bool IsBusy;            // 动画是否正在播放
            AnimAttr_t Attr;        // 动画属性配置
        } Anim;
    } priv;

public:
    /**
     * @brief 虚析构函数
     *
     * 确保派生类对象能够正确析构
     */
    virtual ~PageBase() {}

    /**
     * @brief 同步用户自定义属性配置
     *
     * 在页面加载前调用，用于设置页面的自定义属性
     * 派生类可重写此方法来配置页面特定的属性
     */
    virtual void onCustomAttrConfig() {}

    /**
     * @brief 页面加载开始回调
     *
     * 当页面开始加载时调用，此时页面的根对象已创建
     * 派生类应在此方法中创建页面的UI元素
     */
    virtual void onViewLoad() {}

    /**
     * @brief 页面加载完成回调
     *
     * 当页面加载完成时调用，所有UI元素已创建完毕
     * 派生类可在此方法中进行最终的初始化工作
     */
    virtual void onViewDidLoad() {}

    /**
     * @brief 页面即将显示回调
     *
     * 当页面即将显示时调用，显示动画开始前
     * 派生类可在此方法中准备页面显示前的工作
     */
    virtual void onViewWillAppear() {}

    /**
     * @brief 页面已显示回调
     *
     * 当页面显示动画完成后调用，页面已完全可见
     * 派生类可在此方法中启动定时器、开始数据更新等
     */
    virtual void onViewDidAppear() {}

    /**
     * @brief 页面即将消失回调
     *
     * 当页面即将消失时调用，消失动画开始前
     * 派生类可在此方法中保存数据、停止定时器等
     */
    virtual void onViewWillDisappear() {}

    /**
     * @brief 页面已消失回调
     *
     * 当页面消失动画完成后调用，页面已完全隐藏
     * 派生类可在此方法中进行清理工作
     */
    virtual void onViewDidDisappear() {}

    /**
     * @brief 页面卸载开始回调
     *
     * 当页面开始卸载时调用，UI元素即将被销毁
     * 派生类应在此方法中释放资源、清理数据
     */
    virtual void onViewUnload() {}

    /**
     * @brief 页面卸载完成回调
     *
     * 当页面卸载完成时调用，所有UI元素已被销毁
     * 派生类可在此方法中进行最终的清理工作
     */
    virtual void onViewDidUnload() {}

    /**
     * @brief 设置是否手动管理缓存
     *
     * @param en true：启用手动缓存管理，false：禁用缓存
     *
     * 当启用手动缓存管理时，页面在消失后不会被自动销毁，
     * 而是保留在内存中以便快速重新显示
     */
    void SetCustomCacheEnable(bool en);

    /**
     * @brief 设置是否启用自动缓存管理
     *
     * @param en true：启用自动缓存，false：禁用自动缓存
     *
     * 自动缓存管理由页面管理器根据内存使用情况自动决定
     * 是否缓存页面
     */
    void SetCustomAutoCacheEnable(bool en);

    /**
     * @brief 设置自定义动画属性
     *
     * @param animType 动画类型，参见LoadAnim_t枚举
     * @param time 动画持续时间，单位：毫秒，默认500ms
     * @param path 动画路径函数，默认为缓出效果
     *
     * 设置页面特定的切换动画，会覆盖全局动画设置
     */
    void SetCustomLoadAnimType(
        uint8_t animType,
        uint16_t time = PAGE_ANIM_TIME_DEFAULT,
        lv_anim_path_cb_t path = PAGE_ANIM_PATH_DEFAULT
    );

    /**
     * @brief 从stash区域弹出数据
     *
     * @param ptr 接收数据的缓冲区指针
     * @param size 要弹出的数据大小
     * @return true：成功弹出数据，false：失败（数据不存在或大小不匹配）
     *
     * 用于获取从其他页面传递过来的数据，通常在onViewLoad中调用
     * 使用PAGE_STASH_POP宏可以简化调用
     */
    bool StashPop(void* ptr, uint32_t size);
};

#endif // ! __PAGE_BASE_H
