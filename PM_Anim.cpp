/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#include "PageManager.h"
#include "PM_Log.h"

/**
  * @brief  获取页面加载动画属性
  * @param  anim: 动画类型
  * @param  attr: 属性指针
  * @retval 获取是否成功
  */
bool PageManager::GetLoadAnimAttr(uint8_t anim, LoadAnimAttr_t* attr)
{
    lv_coord_t hor = LV_HOR_RES;  // 水平分辨率
    lv_coord_t ver = LV_VER_RES;  // 垂直分辨率

    switch (anim)
    {
    case LOAD_ANIM_OVER_LEFT:     // 从左侧覆盖动画
        attr->dragDir = ROOT_DRAG_DIR_HOR;

        attr->push.enter.start = hor;    // 推入时进入页面从右侧开始
        attr->push.enter.end = 0;        // 推入时进入页面到中心位置结束
        attr->push.exit.start = 0;       // 推入时退出页面保持在中心
        attr->push.exit.end = 0;         // 推入时退出页面保持在中心

        attr->pop.enter.start = 0;       // 弹出时进入页面从中心开始
        attr->pop.enter.end = 0;         // 弹出时进入页面保持在中心
        attr->pop.exit.start = 0;        // 弹出时退出页面从中心开始
        attr->pop.exit.end = hor;        // 弹出时退出页面向右侧移动
        break;

    case LOAD_ANIM_OVER_RIGHT:    // 从右侧覆盖动画
        attr->dragDir = ROOT_DRAG_DIR_HOR;

        attr->push.enter.start = -hor;   // 推入时进入页面从左侧开始
        attr->push.enter.end = 0;        // 推入时进入页面到中心位置结束
        attr->push.exit.start = 0;       // 推入时退出页面保持在中心
        attr->push.exit.end = 0;         // 推入时退出页面保持在中心

        attr->pop.enter.start = 0;       // 弹出时进入页面从中心开始
        attr->pop.enter.end = 0;         // 弹出时进入页面保持在中心
        attr->pop.exit.start = 0;        // 弹出时退出页面从中心开始
        attr->pop.exit.end = -hor;       // 弹出时退出页面向左侧移动
        break;

    case LOAD_ANIM_OVER_TOP:      // 从顶部覆盖动画
        attr->dragDir = ROOT_DRAG_DIR_VER;

        attr->push.enter.start = ver;    // 推入时进入页面从底部开始
        attr->push.enter.end = 0;        // 推入时进入页面到中心位置结束
        attr->push.exit.start = 0;       // 推入时退出页面保持在中心
        attr->push.exit.end = 0;         // 推入时退出页面保持在中心

        attr->pop.enter.start = 0;       // 弹出时进入页面从中心开始
        attr->pop.enter.end = 0;         // 弹出时进入页面保持在中心
        attr->pop.exit.start = 0;        // 弹出时退出页面从中心开始
        attr->pop.exit.end = ver;        // 弹出时退出页面向底部移动
        break;

    case LOAD_ANIM_OVER_BOTTOM:   // 从底部覆盖动画
        attr->dragDir = ROOT_DRAG_DIR_VER;

        attr->push.enter.start = -ver;   // 推入时进入页面从顶部开始
        attr->push.enter.end = 0;        // 推入时进入页面到中心位置结束
        attr->push.exit.start = 0;       // 推入时退出页面保持在中心
        attr->push.exit.end = 0;         // 推入时退出页面保持在中心

        attr->pop.enter.start = 0;       // 弹出时进入页面从中心开始
        attr->pop.enter.end = 0;         // 弹出时进入页面保持在中心
        attr->pop.exit.start = 0;        // 弹出时退出页面从中心开始
        attr->pop.exit.end = -ver;       // 弹出时退出页面向顶部移动
        break;

    case LOAD_ANIM_MOVE_LEFT:     // 向左推动动画
        attr->dragDir = ROOT_DRAG_DIR_HOR;

        attr->push.enter.start = hor;    // 推入时进入页面从右侧开始
        attr->push.enter.end = 0;        // 推入时进入页面到中心位置结束
        attr->push.exit.start = 0;       // 推入时退出页面从中心开始
        attr->push.exit.end = -hor;      // 推入时退出页面向左侧移动

        attr->pop.enter.start = -hor;    // 弹出时进入页面从左侧开始
        attr->pop.enter.end = 0;         // 弹出时进入页面到中心位置结束
        attr->pop.exit.start = 0;        // 弹出时退出页面从中心开始
        attr->pop.exit.end = hor;        // 弹出时退出页面向右侧移动
        break;

    case LOAD_ANIM_MOVE_RIGHT:    // 向右推动动画
        attr->dragDir = ROOT_DRAG_DIR_HOR;

        attr->push.enter.start = -hor;   // 推入时进入页面从左侧开始
        attr->push.enter.end = 0;        // 推入时进入页面到中心位置结束
        attr->push.exit.start = 0;       // 推入时退出页面从中心开始
        attr->push.exit.end = hor;       // 推入时退出页面向右侧移动

        attr->pop.enter.start = hor;     // 弹出时进入页面从右侧开始
        attr->pop.enter.end = 0;         // 弹出时进入页面到中心位置结束
        attr->pop.exit.start = 0;        // 弹出时退出页面从中心开始
        attr->pop.exit.end = -hor;       // 弹出时退出页面向左侧移动
        break;

    case LOAD_ANIM_MOVE_TOP:      // 向上推动动画
        attr->dragDir = ROOT_DRAG_DIR_VER;

        attr->push.enter.start = ver;    // 推入时进入页面从底部开始
        attr->push.enter.end = 0;        // 推入时进入页面到中心位置结束
        attr->push.exit.start = 0;       // 推入时退出页面从中心开始
        attr->push.exit.end = -ver;      // 推入时退出页面向顶部移动

        attr->pop.enter.start = -ver;    // 弹出时进入页面从顶部开始
        attr->pop.enter.end = 0;         // 弹出时进入页面到中心位置结束
        attr->pop.exit.start = 0;        // 弹出时退出页面从中心开始
        attr->pop.exit.end = ver;        // 弹出时退出页面向底部移动
        break;

    case LOAD_ANIM_MOVE_BOTTOM:   // 向下推动动画
        attr->dragDir = ROOT_DRAG_DIR_VER;

        attr->push.enter.start = -ver;   // 推入时进入页面从顶部开始
        attr->push.enter.end = 0;        // 推入时进入页面到中心位置结束
        attr->push.exit.start = 0;       // 推入时退出页面从中心开始
        attr->push.exit.end = ver;       // 推入时退出页面向底部移动

        attr->pop.enter.start = ver;     // 弹出时进入页面从底部开始
        attr->pop.enter.end = 0;         // 弹出时进入页面到中心位置结束
        attr->pop.exit.start = 0;        // 弹出时退出页面从中心开始
        attr->pop.exit.end = -ver;       // 弹出时退出页面向顶部移动
        break;

    case LOAD_ANIM_FADE_ON:       // 淡入淡出动画
        attr->dragDir = ROOT_DRAG_DIR_NONE;

        attr->push.enter.start = LV_OPA_TRANSP;  // 推入时进入页面从透明开始
        attr->push.enter.end = LV_OPA_COVER;     // 推入时进入页面到完全不透明结束
        attr->push.exit.start = LV_OPA_COVER;    // 推入时退出页面保持完全不透明
        attr->push.exit.end = LV_OPA_COVER;      // 推入时退出页面保持完全不透明

        attr->pop.enter.start = LV_OPA_COVER;    // 弹出时进入页面保持完全不透明
        attr->pop.enter.end = LV_OPA_COVER;      // 弹出时进入页面保持完全不透明
        attr->pop.exit.start = LV_OPA_COVER;     // 弹出时退出页面从完全不透明开始
        attr->pop.exit.end = LV_OPA_TRANSP;      // 弹出时退出页面到透明结束
        break;

    case LOAD_ANIM_NONE:          // 无动画
        memset(attr, 0, sizeof(LoadAnimAttr_t));
        return true;

    default:
        PM_LOG_ERROR("Load anim type error: %d", anim);
        return false;
    }

    /* 确定动画的设置器和获取器 */
    if (attr->dragDir == ROOT_DRAG_DIR_HOR)
    {
        /* 水平方向动画：设置和获取X坐标 */
        attr->setter = [](void* obj, int32_t v)
        {
            lv_obj_set_x((lv_obj_t*)obj, v);
        };
        attr->getter = [](void* obj)
        {
            return (int32_t)lv_obj_get_x((lv_obj_t*)obj);
        };
    }
    else if (attr->dragDir == ROOT_DRAG_DIR_VER)
    {
        /* 垂直方向动画：设置和获取Y坐标 */
        attr->setter = [](void* obj, int32_t v)
        {
            lv_obj_set_y((lv_obj_t*)obj, v);
        };
        attr->getter = [](void* obj)
        {
            return (int32_t)lv_obj_get_y((lv_obj_t*)obj);
        };
    }
    else
    {
        /* 透明度动画：设置和获取背景透明度 */
        attr->setter = [](void* obj, int32_t v)
        {
            lv_obj_set_style_bg_opa((lv_obj_t*)obj, (lv_opa_t)v, LV_PART_MAIN);
        };
        attr->getter = [](void* obj)
        {
            return (int32_t)lv_obj_get_style_bg_opa((lv_obj_t*)obj, LV_PART_MAIN);
        };
    }

    return true;
}
