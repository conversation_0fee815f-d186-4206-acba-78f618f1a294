# Makefile for Dialplate C Language Port
# MIT License - Copyright (c) 2021 _VIFEXTech

# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS = 

# 项目名称
PROJECT_NAME = dialplate_c

# 源文件目录
SRC_DIR = .
BUILD_DIR = build
OBJ_DIR = $(BUILD_DIR)/obj

# 页面管理器源文件
PM_SOURCES = \
	page_manager_c.c \
	pm_router_c.c \
	pm_state_c.c \
	pm_anim_c.c \
	pm_drag_c.c \
	pm_page_base_c.c \
	pm_factory_c.c

# 表盘模块源文件
DIALPLATE_SOURCES = \
	dialplate_c.c \
	dialplate_model_c.c \
	dialplate_view_c.c

# 示例和测试源文件
EXAMPLE_SOURCES = \
	dialplate_example_c.c \
	example_usage.c \
	test_page_manager.c

# 所有源文件
SOURCES = $(PM_SOURCES) $(DIALPLATE_SOURCES) $(EXAMPLE_SOURCES)

# 对象文件
OBJECTS = $(SOURCES:%.c=$(OBJ_DIR)/%.o)

# 头文件
HEADERS = \
	page_manager_c.h \
	dialplate_c.h \
	pm_log_c.h

# 目标文件
TARGETS = \
	$(BUILD_DIR)/dialplate_example \
	$(BUILD_DIR)/page_manager_test \
	$(BUILD_DIR)/libdialplate_c.a

# LVGL相关设置（需要根据实际LVGL安装路径调整）
LVGL_DIR = /usr/local/include/lvgl
LVGL_LIBS = -llvgl

# 如果LVGL不在系统路径中，取消注释并调整路径
# CFLAGS += -I$(LVGL_DIR)
# LDFLAGS += -L$(LVGL_DIR)/lib

# 包含路径
CFLAGS += -I$(SRC_DIR)

# 默认目标
all: $(TARGETS)

# 创建构建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

$(OBJ_DIR):
	@mkdir -p $(OBJ_DIR)

# 编译对象文件
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.c $(HEADERS) | $(OBJ_DIR)
	@echo "Compiling $<..."
	@$(CC) $(CFLAGS) -c $< -o $@

# 构建静态库
$(BUILD_DIR)/libdialplate_c.a: $(filter $(OBJ_DIR)/page_manager_c.o $(OBJ_DIR)/pm_%.o $(OBJ_DIR)/dialplate%.o, $(OBJECTS)) | $(BUILD_DIR)
	@echo "Creating static library $@..."
	@ar rcs $@ $^

# 构建表盘示例
$(BUILD_DIR)/dialplate_example: $(filter-out $(OBJ_DIR)/test_page_manager.o $(OBJ_DIR)/example_usage.o, $(OBJECTS)) | $(BUILD_DIR)
	@echo "Linking $@..."
	@$(CC) $^ -o $@ $(LDFLAGS) $(LVGL_LIBS)

# 构建页面管理器测试
$(BUILD_DIR)/page_manager_test: $(filter $(OBJ_DIR)/page_manager_c.o $(OBJ_DIR)/pm_%.o $(OBJ_DIR)/test_page_manager.o, $(OBJECTS)) | $(BUILD_DIR)
	@echo "Linking $@..."
	@$(CC) $^ -o $@ $(LDFLAGS) $(LVGL_LIBS)

# 清理
clean:
	@echo "Cleaning..."
	@rm -rf $(BUILD_DIR)

# 重新构建
rebuild: clean all

# 安装头文件和库（可选）
install: $(BUILD_DIR)/libdialplate_c.a
	@echo "Installing..."
	@mkdir -p /usr/local/include/dialplate_c
	@cp $(HEADERS) /usr/local/include/dialplate_c/
	@cp $(BUILD_DIR)/libdialplate_c.a /usr/local/lib/
	@echo "Installation complete."

# 卸载
uninstall:
	@echo "Uninstalling..."
	@rm -rf /usr/local/include/dialplate_c
	@rm -f /usr/local/lib/libdialplate_c.a
	@echo "Uninstallation complete."

# 运行测试
test: $(BUILD_DIR)/page_manager_test
	@echo "Running page manager tests..."
	@$(BUILD_DIR)/page_manager_test

# 运行示例（需要LVGL环境）
run-example: $(BUILD_DIR)/dialplate_example
	@echo "Running dialplate example..."
	@$(BUILD_DIR)/dialplate_example

# 代码格式化（需要安装clang-format）
format:
	@echo "Formatting code..."
	@find $(SRC_DIR) -name "*.c" -o -name "*.h" | xargs clang-format -i

# 代码检查（需要安装cppcheck）
check:
	@echo "Running static analysis..."
	@cppcheck --enable=all --std=c99 $(SRC_DIR)/*.c

# 生成文档（需要安装doxygen）
docs:
	@echo "Generating documentation..."
	@doxygen Doxyfile

# 显示帮助信息
help:
	@echo "Available targets:"
	@echo "  all          - Build all targets"
	@echo "  clean        - Remove build directory"
	@echo "  rebuild      - Clean and build all"
	@echo "  install      - Install headers and library"
	@echo "  uninstall    - Remove installed files"
	@echo "  test         - Run page manager tests"
	@echo "  run-example  - Run dialplate example (requires LVGL)"
	@echo "  format       - Format source code (requires clang-format)"
	@echo "  check        - Run static analysis (requires cppcheck)"
	@echo "  docs         - Generate documentation (requires doxygen)"
	@echo "  help         - Show this help message"

# 调试信息
debug-info:
	@echo "Project: $(PROJECT_NAME)"
	@echo "Sources: $(SOURCES)"
	@echo "Objects: $(OBJECTS)"
	@echo "Targets: $(TARGETS)"
	@echo "CFLAGS: $(CFLAGS)"
	@echo "LDFLAGS: $(LDFLAGS)"

# 依赖关系
.PHONY: all clean rebuild install uninstall test run-example format check docs help debug-info

# 包含依赖文件（如果存在）
-include $(OBJECTS:.o=.d)

# 生成依赖文件
$(OBJ_DIR)/%.d: $(SRC_DIR)/%.c | $(OBJ_DIR)
	@$(CC) $(CFLAGS) -MM -MT $(@:.d=.o) $< > $@
