/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Dialplate Implementation
 */
#include "dialplate_c.h"
#include "pm_log_c.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* 表盘虚函数表 */
static pm_page_vtable_t dialplate_vtable = {
    .on_custom_attr_config = dialplate_on_custom_attr_config,
    .on_view_load = dialplate_on_view_load,
    .on_view_did_load = dialplate_on_view_did_load,
    .on_view_will_appear = dialplate_on_view_will_appear,
    .on_view_did_appear = dialplate_on_view_did_appear,
    .on_view_will_disappear = dialplate_on_view_will_disappear,
    .on_view_did_disappear = dialplate_on_view_did_disappear,
    .on_view_unload = dialplate_on_view_unload,
    .on_view_did_unload = dialplate_on_view_did_unload
};

/* ========== 表盘主类实现 ========== */

/**
 * @brief 创建表盘对象
 * @retval 表盘对象指针，失败返回NULL
 */
dialplate_c_t* dialplate_create(void)
{
    dialplate_c_t* dialplate = (dialplate_c_t*)malloc(sizeof(dialplate_c_t));
    if (dialplate == NULL) {
        PM_LOG_ERROR("Failed to allocate memory for dialplate");
        return NULL;
    }
    
    /* 初始化成员变量 */
    memset(dialplate, 0, sizeof(dialplate_c_t));
    
    /* 初始化基类 */
    pm_page_base_t* base = pm_page_base_create();
    if (base == NULL) {
        free(dialplate);
        PM_LOG_ERROR("Failed to create page base");
        return NULL;
    }
    
    dialplate->base = *base;
    free(base);  // 释放临时创建的基类
    
    /* 设置虚函数表 */
    pm_page_base_set_vtable(&dialplate->base, &dialplate_vtable);
    
    /* 初始化表盘特有成员 */
    dialplate->rec_state = DIALPLATE_RECORD_STATE_READY;
    dialplate->last_focus = NULL;
    dialplate->timer = NULL;
    
    /* 创建模型和视图 */
    dialplate_model_c_t* model = dialplate_model_create();
    dialplate_view_c_t* view = dialplate_view_create();
    
    if (model == NULL || view == NULL) {
        if (model) dialplate_model_destroy(model);
        if (view) dialplate_view_destroy(view);
        pm_page_base_destroy(&dialplate->base);
        free(dialplate);
        PM_LOG_ERROR("Failed to create model or view");
        return NULL;
    }
    
    dialplate->model = *model;
    dialplate->view = *view;
    free(model);
    free(view);
    
    PM_LOG_INFO("Dialplate created successfully");
    return dialplate;
}

/**
 * @brief 销毁表盘对象
 * @param dialplate 表盘对象指针
 */
void dialplate_destroy(dialplate_c_t* dialplate)
{
    if (dialplate == NULL) {
        return;
    }
    
    /* 销毁定时器 */
    if (dialplate->timer) {
        lv_timer_del(dialplate->timer);
        dialplate->timer = NULL;
    }
    
    /* 销毁模型和视图 */
    dialplate_model_deinit(&dialplate->model);
    dialplate_view_delete_ui(&dialplate->view);
    
    /* 销毁基类 */
    pm_page_base_destroy(&dialplate->base);
    
    free(dialplate);
    PM_LOG_INFO("Dialplate destroyed");
}

/* ========== 页面生命周期回调实现 ========== */

/**
 * @brief 自定义属性配置回调
 * @param self 页面基类指针
 */
void dialplate_on_custom_attr_config(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    /* 设置无动画加载 */
    pm_page_base_set_custom_load_anim_type(self, PM_LOAD_ANIM_NONE, 0, NULL);
    
    PM_LOG_INFO("Dialplate custom attr config");
}

/**
 * @brief 页面加载回调
 * @param self 页面基类指针
 */
void dialplate_on_view_load(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    dialplate_c_t* dialplate = (dialplate_c_t*)self;
    
    /* 初始化模型 */
    dialplate_model_init(&dialplate->model);
    
    /* 创建视图UI */
    dialplate_view_create_ui(&dialplate->view, self->root);
    
    /* 绑定事件 */
    dialplate_attach_event(dialplate, dialplate->view.ui.btn_cont.btn_map);
    dialplate_attach_event(dialplate, dialplate->view.ui.btn_cont.btn_rec);
    dialplate_attach_event(dialplate, dialplate->view.ui.btn_cont.btn_menu);
    
    PM_LOG_INFO("Dialplate view loaded");
}

/**
 * @brief 页面加载完成回调
 * @param self 页面基类指针
 */
void dialplate_on_view_did_load(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    PM_LOG_INFO("Dialplate view did load");
}

/**
 * @brief 页面即将显示回调
 * @param self 页面基类指针
 */
void dialplate_on_view_will_appear(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    dialplate_c_t* dialplate = (dialplate_c_t*)self;
    
    /* 等待输入设备释放 */
    lv_indev_wait_release(lv_indev_get_act());
    
    /* 设置焦点组 */
    lv_group_t* group = lv_group_get_default();
    if (group) {
        lv_group_set_wrap(group, false);
        
        lv_group_add_obj(group, dialplate->view.ui.btn_cont.btn_map);
        lv_group_add_obj(group, dialplate->view.ui.btn_cont.btn_rec);
        lv_group_add_obj(group, dialplate->view.ui.btn_cont.btn_menu);
        
        if (dialplate->last_focus) {
            lv_group_focus_obj(dialplate->last_focus);
        } else {
            lv_group_focus_obj(dialplate->view.ui.btn_cont.btn_rec);
        }
    }
    
    /* 设置状态栏样式为透明 */
    dialplate_model_set_status_bar_style(&dialplate->model, 0); // 0 = TRANSP
    
    /* 更新显示 */
    dialplate_update(dialplate);
    
    /* 启动显示动画 */
    dialplate_view_appear_anim_start(&dialplate->view, false);
    
    PM_LOG_INFO("Dialplate will appear");
}

/**
 * @brief 页面已显示回调
 * @param self 页面基类指针
 */
void dialplate_on_view_did_appear(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    dialplate_c_t* dialplate = (dialplate_c_t*)self;
    
    /* 创建更新定时器 */
    dialplate->timer = lv_timer_create(dialplate_on_timer_update, 1000, dialplate);
    
    PM_LOG_INFO("Dialplate did appear");
}

/**
 * @brief 页面即将消失回调
 * @param self 页面基类指针
 */
void dialplate_on_view_will_disappear(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    dialplate_c_t* dialplate = (dialplate_c_t*)self;
    
    /* 保存当前焦点 */
    lv_group_t* group = lv_group_get_default();
    if (group) {
        dialplate->last_focus = lv_group_get_focused(group);
        lv_group_remove_all_objs(group);
    }
    
    /* 删除定时器 */
    if (dialplate->timer) {
        lv_timer_del(dialplate->timer);
        dialplate->timer = NULL;
    }
    
    PM_LOG_INFO("Dialplate will disappear");
}

/**
 * @brief 页面已消失回调
 * @param self 页面基类指针
 */
void dialplate_on_view_did_disappear(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    PM_LOG_INFO("Dialplate did disappear");
}

/**
 * @brief 页面卸载回调
 * @param self 页面基类指针
 */
void dialplate_on_view_unload(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    dialplate_c_t* dialplate = (dialplate_c_t*)self;
    
    /* 反初始化模型 */
    dialplate_model_deinit(&dialplate->model);
    
    /* 删除视图UI */
    dialplate_view_delete_ui(&dialplate->view);
    
    PM_LOG_INFO("Dialplate view unloaded");
}

/**
 * @brief 页面卸载完成回调
 * @param self 页面基类指针
 */
void dialplate_on_view_did_unload(pm_page_base_t* self)
{
    if (self == NULL) return;
    
    PM_LOG_INFO("Dialplate view did unload");
}

/* ========== 内部功能函数实现 ========== */

/**
 * @brief 更新表盘显示
 * @param dialplate 表盘对象指针
 */
void dialplate_update(dialplate_c_t* dialplate)
{
    if (dialplate == NULL) return;
    
    char buf[32];
    
    /* 更新速度显示 */
    int speed = (int)dialplate_model_get_speed(&dialplate->model);
    dialplate_view_update_speed(&dialplate->view, speed);
    
    /* 更新平均速度 */
    snprintf(buf, sizeof(buf), "%.1f km/h", dialplate_model_get_avg_speed(&dialplate->model));
    dialplate_view_update_info(&dialplate->view, 0, buf);
    
    /* 更新运动时间 */
    dialplate_make_time_string(dialplate->model.sport_status_info.single_time, buf, sizeof(buf));
    dialplate_view_update_info(&dialplate->view, 1, buf);
    
    /* 更新运动距离 */
    snprintf(buf, sizeof(buf), "%.1f km", dialplate->model.sport_status_info.single_distance / 1000.0f);
    dialplate_view_update_info(&dialplate->view, 2, buf);
    
    /* 更新卡路里 */
    snprintf(buf, sizeof(buf), "%d k", (int)dialplate->model.sport_status_info.single_calorie);
    dialplate_view_update_info(&dialplate->view, 3, buf);
}

/**
 * @brief 绑定事件到对象
 * @param dialplate 表盘对象指针
 * @param obj 要绑定事件的对象
 */
void dialplate_attach_event(dialplate_c_t* dialplate, lv_obj_t* obj)
{
    if (dialplate == NULL || obj == NULL) return;
    
    lv_obj_add_event_cb(obj, dialplate_on_event, LV_EVENT_ALL, dialplate);
}

/**
 * @brief 定时器更新回调
 * @param timer 定时器指针
 */
void dialplate_on_timer_update(lv_timer_t* timer)
{
    if (timer == NULL || timer->user_data == NULL) return;
    
    dialplate_c_t* dialplate = (dialplate_c_t*)timer->user_data;
    dialplate_update(dialplate);
}

/* ========== 工具函数实现 ========== */

/**
 * @brief 格式化时间字符串
 * @param time_sec 时间（秒）
 * @param buf 缓冲区
 * @param buf_size 缓冲区大小
 * @retval 格式化后的时间字符串
 */
const char* dialplate_make_time_string(uint32_t time_sec, char* buf, size_t buf_size)
{
    if (buf == NULL || buf_size == 0) return "";
    
    uint32_t hours = time_sec / 3600;
    uint32_t minutes = (time_sec % 3600) / 60;
    uint32_t seconds = time_sec % 60;
    
    if (hours > 0) {
        snprintf(buf, buf_size, "%02d:%02d:%02d", hours, minutes, seconds);
    } else {
        snprintf(buf, buf_size, "%02d:%02d", minutes, seconds);
    }
    
    return buf;
}

/**
 * @brief 获取图片资源（需要根据实际资源系统实现）
 * @param name 图片名称
 * @retval 图片资源指针
 */
const void* dialplate_get_image(const char* name)
{
    /* 这里需要根据实际的资源管理系统实现 */
    /* 示例实现，实际使用时需要替换 */
    PM_LOG_INFO("Getting image: %s", name ? name : "NULL");
    return NULL; // 返回实际的图片资源
}

/**
 * @brief 获取字体资源（需要根据实际资源系统实现）
 * @param name 字体名称
 * @retval 字体资源指针
 */
const lv_font_t* dialplate_get_font(const char* name)
{
    /* 这里需要根据实际的字体管理系统实现 */
    /* 示例实现，实际使用时需要替换 */
    PM_LOG_INFO("Getting font: %s", name ? name : "NULL");
    return &lv_font_montserrat_14; // 返回默认字体
}

/* ========== 事件处理函数实现 ========== */

/**
 * @brief 按钮点击处理
 * @param dialplate 表盘对象指针
 * @param btn 被点击的按钮
 */
void dialplate_on_btn_clicked(dialplate_c_t* dialplate, lv_obj_t* btn)
{
    if (dialplate == NULL || btn == NULL) return;

    pm_page_manager_t* manager = pm_page_base_get_manager(&dialplate->base);
    if (manager == NULL) return;

    if (btn == dialplate->view.ui.btn_cont.btn_map) {
        /* 跳转到地图页面 */
        pm_page_manager_push(manager, "LiveMap", NULL);
        PM_LOG_INFO("Navigate to LiveMap page");
    } else if (btn == dialplate->view.ui.btn_cont.btn_menu) {
        /* 跳转到系统信息页面 */
        pm_page_manager_push(manager, "SystemInfos", NULL);
        PM_LOG_INFO("Navigate to SystemInfos page");
    }
}

/**
 * @brief 录制控制处理
 * @param dialplate 表盘对象指针
 * @param long_press 是否为长按
 */
void dialplate_on_record(dialplate_c_t* dialplate, bool long_press)
{
    if (dialplate == NULL) return;

    switch (dialplate->rec_state) {
    case DIALPLATE_RECORD_STATE_READY:
        if (long_press) {
            /* 检查GPS是否准备好 */
            if (!dialplate_model_get_gps_ready(&dialplate->model)) {
                PM_LOG_WARN("GPS has not ready, can't start record");
                dialplate_model_play_music(&dialplate->model, "Error");
                return;
            }

            /* 开始录制 */
            dialplate_model_play_music(&dialplate->model, "Connect");
            dialplate_model_recorder_command(&dialplate->model, DIALPLATE_REC_START);
            dialplate_view_set_btn_rec_img_src(&dialplate->view, "pause");
            dialplate->rec_state = DIALPLATE_RECORD_STATE_RUN;
            PM_LOG_INFO("Recording started");
        }
        break;

    case DIALPLATE_RECORD_STATE_RUN:
        if (!long_press) {
            /* 暂停录制 */
            dialplate_model_play_music(&dialplate->model, "UnstableConnect");
            dialplate_model_recorder_command(&dialplate->model, DIALPLATE_REC_PAUSE);
            dialplate_view_set_btn_rec_img_src(&dialplate->view, "start");
            dialplate->rec_state = DIALPLATE_RECORD_STATE_PAUSE;
            PM_LOG_INFO("Recording paused");
        }
        break;

    case DIALPLATE_RECORD_STATE_PAUSE:
        if (long_press) {
            /* 准备停止录制 */
            dialplate_model_play_music(&dialplate->model, "NoOperationWarning");
            dialplate_view_set_btn_rec_img_src(&dialplate->view, "stop");
            dialplate_model_recorder_command(&dialplate->model, DIALPLATE_REC_READY_STOP);
            dialplate->rec_state = DIALPLATE_RECORD_STATE_STOP;
            PM_LOG_INFO("Recording ready to stop");
        } else {
            /* 继续录制 */
            dialplate_model_play_music(&dialplate->model, "Connect");
            dialplate_model_recorder_command(&dialplate->model, DIALPLATE_REC_CONTINUE);
            dialplate_view_set_btn_rec_img_src(&dialplate->view, "pause");
            dialplate->rec_state = DIALPLATE_RECORD_STATE_RUN;
            PM_LOG_INFO("Recording continued");
        }
        break;

    case DIALPLATE_RECORD_STATE_STOP:
        if (long_press) {
            /* 停止录制 */
            dialplate_model_play_music(&dialplate->model, "Disconnect");
            dialplate_model_recorder_command(&dialplate->model, DIALPLATE_REC_STOP);
            dialplate_view_set_btn_rec_img_src(&dialplate->view, "start");
            dialplate->rec_state = DIALPLATE_RECORD_STATE_READY;
            PM_LOG_INFO("Recording stopped");
        } else {
            /* 继续录制 */
            dialplate_model_play_music(&dialplate->model, "Connect");
            dialplate_model_recorder_command(&dialplate->model, DIALPLATE_REC_CONTINUE);
            dialplate_view_set_btn_rec_img_src(&dialplate->view, "pause");
            dialplate->rec_state = DIALPLATE_RECORD_STATE_RUN;
            PM_LOG_INFO("Recording continued from stop state");
        }
        break;

    default:
        PM_LOG_ERROR("Unknown record state: %d", dialplate->rec_state);
        break;
    }
}

/**
 * @brief 事件回调函数
 * @param event 事件指针
 */
void dialplate_on_event(lv_event_t* event)
{
    if (event == NULL) return;

    dialplate_c_t* dialplate = (dialplate_c_t*)lv_event_get_user_data(event);
    if (dialplate == NULL) return;

    lv_obj_t* obj = lv_event_get_current_target(event);
    lv_event_code_t code = lv_event_get_code(event);

    /* 处理短按事件 */
    if (code == LV_EVENT_SHORT_CLICKED) {
        dialplate_on_btn_clicked(dialplate, obj);
    }

    /* 处理录制按钮的特殊事件 */
    if (obj == dialplate->view.ui.btn_cont.btn_rec) {
        if (code == LV_EVENT_SHORT_CLICKED) {
            dialplate_on_record(dialplate, false);
        } else if (code == LV_EVENT_LONG_PRESSED) {
            dialplate_on_record(dialplate, true);
        }
    }
}
