/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#include "PageManager.h"
#include "PM_Log.h"
#include <stdlib.h>

#define CONSTRAIN(amt,low,high) ((amt)<(low)?(low):((amt)>(high)?(high):(amt)))

/* 触发拖拽的距离阈值 */
#define PM_INDEV_DEF_DRAG_THROW    20

/**
  * @brief  页面拖拽事件回调
  * @param  event: 事件结构体指针
  * @retval None
  */
void PageManager::onRootDragEvent(lv_event_t* event)
{
    lv_event_code_t eventCode = lv_event_get_code(event);

    /* 只处理按下、按压和释放事件 */
    if (!(eventCode == LV_EVENT_PRESSED || eventCode == LV_EVENT_PRESSING || eventCode == LV_EVENT_RELEASED))
    {
        return;
    }

    lv_obj_t* root = lv_event_get_current_target(event);
    PageBase* base = (PageBase*)lv_event_get_user_data(event);

    if (base == nullptr)
    {
        PM_LOG_ERROR("Page base is NULL");
        return;
    }

    PageManager* manager = base->_Manager;
    LoadAnimAttr_t animAttr;

    /* 获取当前动画属性 */
    if (!manager->GetCurrentLoadAnimAttr(&animAttr))
    {
        PM_LOG_ERROR("Can't get current anim attr");
        return;
    }

    if (eventCode == LV_EVENT_PRESSED)
    {
        /* 如果有切换请求，则忽略 */
        if (manager->_AnimState.IsSwitchReq)
        {
            return;
        }

        /* 如果动画不忙，则忽略 */
        if (!manager->_AnimState.IsBusy)
        {
            return;
        }

        PM_LOG_INFO("Root anim interrupted");
        /* 删除当前动画 */
        lv_anim_del(root, animAttr.setter);
        manager->_AnimState.IsBusy = false;

        /* 临时显示底部页面 */
        PageBase* bottomPage = manager->GetStackTopAfter();
        lv_obj_clear_flag(bottomPage->_root, LV_OBJ_FLAG_HIDDEN);
    }
    else if (eventCode == LV_EVENT_PRESSING)
    {
        /* 获取当前位置 */
        lv_coord_t cur = animAttr.getter(root);

        /* 计算拖拽范围的最大值和最小值 */
        lv_coord_t max = std::max(animAttr.pop.exit.start, animAttr.pop.exit.end);
        lv_coord_t min = std::min(animAttr.pop.exit.start, animAttr.pop.exit.end);

        /* 获取输入设备的偏移量 */
        lv_point_t offset;
        lv_indev_get_vect(lv_indev_get_act(), &offset);

        /* 根据拖拽方向更新位置 */
        if (animAttr.dragDir == ROOT_DRAG_DIR_HOR)
        {
            cur += offset.x;  // 水平拖拽
        }
        else if (animAttr.dragDir == ROOT_DRAG_DIR_VER)
        {
            cur += offset.y;  // 垂直拖拽
        }

        /* 限制在有效范围内并设置新位置 */
        animAttr.setter(root, CONSTRAIN(cur, min, max));
    }
    else if (eventCode == LV_EVENT_RELEASED)
    {
        /* 如果有切换请求，则忽略 */
        if (manager->_AnimState.IsSwitchReq)
        {
            return;
        }

        /* 计算总偏移量 */
        lv_coord_t offset_sum = animAttr.push.enter.end - animAttr.push.enter.start;

        /* 获取拖拽惯性预测停止点 */
        lv_coord_t x_predict = 0;
        lv_coord_t y_predict = 0;
        RootGetDragPredict(&x_predict, &y_predict);

        /* 获取当前位置并计算预测结束位置 */
        lv_coord_t start = animAttr.getter(root);
        lv_coord_t end = start;

        if (animAttr.dragDir == ROOT_DRAG_DIR_HOR)
        {
            end += x_predict;  // 水平方向预测
            PM_LOG_INFO("Root drag x_predict = %d", end);
        }
        else if (animAttr.dragDir == ROOT_DRAG_DIR_VER)
        {
            end += y_predict;  // 垂直方向预测
            PM_LOG_INFO("Root drag y_predict = %d", end);
        }

        /* 判断是否超过阈值，决定是否触发页面离开 */
        if (std::abs(end) > std::abs((int)offset_sum) / 2)
        {
            /* 异步调用页面离开事件 */
            lv_async_call(onRootAsyncLeave, base);
        }
        else if(end != animAttr.push.enter.end)
        {
            /* 回弹动画：页面回到原位置 */
            manager->_AnimState.IsBusy = true;

            lv_anim_t a;
            manager->AnimDefaultInit(&a);
            lv_anim_set_user_data(&a, manager);
            lv_anim_set_var(&a, root);
            lv_anim_set_values(&a, start, animAttr.push.enter.end);
            lv_anim_set_exec_cb(&a, animAttr.setter);
            lv_anim_set_ready_cb(&a, onRootDragAnimFinish);
            lv_anim_start(&a);
            PM_LOG_INFO("Root drag anim start");
        }
    }
}

/**
  * @brief  拖拽动画结束事件回调
  * @param  a: 动画指针
  * @retval None
  */
void PageManager::onRootDragAnimFinish(lv_anim_t* a)
{
    PageManager* manager = (PageManager*)lv_anim_get_user_data(a);
    PM_LOG_INFO("Root drag anim finish");
    manager->_AnimState.IsBusy = false;

    /* 隐藏底部页面 */
    PageBase* bottomPage = manager->GetStackTopAfter();
    if (bottomPage)
    {
        lv_obj_add_flag(bottomPage->_root, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
  * @brief  启用根对象的拖拽功能
  * @param  root: 根对象指针
  * @retval None
  */
void PageManager::RootEnableDrag(lv_obj_t* root)
{
    PageBase* base = (PageBase*)lv_obj_get_user_data(root);
    /* 为根对象添加拖拽事件回调 */
    lv_obj_add_event_cb(
        root,
        onRootDragEvent,
        LV_EVENT_ALL,
        base
    );
    PM_LOG_INFO("Page(%s) Root drag enabled", base->_Name);
}

/**
  * @brief  拖拽结束时的异步回调
  * @param  data: 页面基类指针
  * @retval None
  */
void PageManager::onRootAsyncLeave(void* data)
{
    PageBase* base = (PageBase*)data;
    PM_LOG_INFO("Page(%s) send event: LV_EVENT_LEAVE, need to handle...", base->_Name);
    /* 发送页面离开事件 */
    lv_event_send(base->_root, LV_EVENT_LEAVE, base);
}

/**
  * @brief  获取拖拽惯性预测停止点
  * @param  x: x轴停止点
  * @param  y: y轴停止点
  * @retval None
  */
void PageManager::RootGetDragPredict(lv_coord_t* x, lv_coord_t* y)
{
    lv_indev_t* indev = lv_indev_get_act();
    lv_point_t vect;
    lv_indev_get_vect(indev, &vect);

    lv_coord_t y_predict = 0;
    lv_coord_t x_predict = 0;

    /* 计算Y轴惯性滑动距离 */
    while (vect.y != 0)
    {
        y_predict += vect.y;
        /* 应用阻尼系数，模拟摩擦力 */
        vect.y = vect.y * (100 - PM_INDEV_DEF_DRAG_THROW) / 100;
    }

    /* 计算X轴惯性滑动距离 */
    while (vect.x != 0)
    {
        x_predict += vect.x;
        /* 应用阻尼系数，模拟摩擦力 */
        vect.x = vect.x * (100 - PM_INDEV_DEF_DRAG_THROW) / 100;
    }

    *x = x_predict;
    *y = y_predict;
}
