/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#ifndef __PAGE_MANAGER_H
#define __PAGE_MANAGER_H

#include "PageBase.h"
#include "PageFactory.h"
#include <vector>
#include <stack>

class PageManager
{
public:

    /* 页面切换动画类型 */
    typedef enum
    {
        /* 默认（全局）动画类型 */
        LOAD_ANIM_GLOBAL = 0,

        /* 新页面覆盖旧页面 */
        LOAD_ANIM_OVER_LEFT,        // 从左侧覆盖
        LOAD_ANIM_OVER_RIGHT,       // 从右侧覆盖
        LOAD_ANIM_OVER_TOP,         // 从顶部覆盖
        LOAD_ANIM_OVER_BOTTOM,      // 从底部覆盖

        /* 新页面推动旧页面 */
        LOAD_ANIM_MOVE_LEFT,        // 向左推动
        LOAD_ANIM_MOVE_RIGHT,       // 向右推动
        LOAD_ANIM_MOVE_TOP,         // 向上推动
        LOAD_ANIM_MOVE_BOTTOM,      // 向下推动

        /* 新界面淡入，旧页面淡出 */
        LOAD_ANIM_FADE_ON,

        /* 无动画 */
        LOAD_ANIM_NONE,

        _LOAD_ANIM_LAST = LOAD_ANIM_NONE
    } LoadAnim_t;

    /* 页面拖拽方向 */
    typedef enum
    {
        ROOT_DRAG_DIR_NONE,         // 无拖拽方向
        ROOT_DRAG_DIR_HOR,          // 水平拖拽
        ROOT_DRAG_DIR_VER,          // 垂直拖拽
    } RootDragDir_t;

    /* 动画设置器函数指针 */
    typedef void(*lv_anim_setter_t)(void*, int32_t);

    /* 动画获取器函数指针 */
    typedef int32_t(*lv_anim_getter_t)(void*);

    /* 动画切换记录 */
    typedef struct
    {
        /* 作为进入方 */
        struct
        {
            int32_t start;              // 动画起始值
            int32_t end;                // 动画结束值
        } enter;

        /* 作为退出方 */
        struct
        {
            int32_t start;              // 动画起始值
            int32_t end;                // 动画结束值
        } exit;
    } AnimValue_t;

    /* 页面加载动画属性 */
    typedef struct
    {
        lv_anim_setter_t setter;        // 动画设置器函数
        lv_anim_getter_t getter;        // 动画获取器函数
        RootDragDir_t dragDir;          // 拖拽方向
        AnimValue_t push;               // 推入动画值
        AnimValue_t pop;                // 弹出动画值
    } LoadAnimAttr_t;

public:
    PageManager(PageFactory* factory = nullptr);
    ~PageManager();

    /* Loader */
    bool Install(const char* className, const char* appName);
    bool Uninstall(const char* appName);
    bool Register(PageBase* base, const char* name);
    bool Unregister(const char* name);

    /* Router */
    bool Replace(const char* name, const PageBase::Stash_t* stash = nullptr);
    bool Push(const char* name, const PageBase::Stash_t* stash = nullptr);
    bool Pop();
    bool BackHome();
    const char* GetPagePrevName();

    /* Global Animation */
    void SetGlobalLoadAnimType(
        LoadAnim_t anim = LOAD_ANIM_OVER_LEFT,
        uint16_t time = 500,
        lv_anim_path_cb_t path = lv_anim_path_ease_out
    );

    void SetRootDefaultStyle(lv_style_t* style)
    {
        _RootDefaultStyle = style;
    }

private:
    /* Page Pool */
    PageBase* FindPageInPool(const char* name);

    /* Page Stack */
    PageBase* FindPageInStack(const char* name);
    PageBase* GetStackTop();
    PageBase* GetStackTopAfter();
    void SetStackClear(bool keepBottom = false);
    bool FourceUnload(PageBase* base);

    /* Animation */
    bool GetLoadAnimAttr(uint8_t anim, LoadAnimAttr_t* attr);
    bool GetIsOverAnim(uint8_t anim)
    {
        return (anim >= LOAD_ANIM_OVER_LEFT && anim <= LOAD_ANIM_OVER_BOTTOM);
    }
    bool GetIsMoveAnim(uint8_t anim)
    {
        return (anim >= LOAD_ANIM_MOVE_LEFT && anim <= LOAD_ANIM_MOVE_BOTTOM);
    }
    void AnimDefaultInit(lv_anim_t* a);
    bool GetCurrentLoadAnimAttr(LoadAnimAttr_t* attr)
    {
        return GetLoadAnimAttr(GetCurrentLoadAnimType(), attr);
    }
    LoadAnim_t GetCurrentLoadAnimType()
    {
        return (LoadAnim_t)_AnimState.Current.Type;
    }

    /* Root */
    static void onRootDragEvent(lv_event_t* event);
    static void onRootDragAnimFinish(lv_anim_t* a);
    static void onRootAsyncLeave(void* base);
    void RootEnableDrag(lv_obj_t* root);
    static void RootGetDragPredict(lv_coord_t* x, lv_coord_t* y);

    /* Switch */
    bool SwitchTo(PageBase* base, bool isEnterAct, const PageBase::Stash_t* stash = nullptr);
    static void onSwitchAnimFinish(lv_anim_t* a);
    void SwitchAnimCreate(PageBase* base);
    void SwitchAnimTypeUpdate(PageBase* base);
    bool SwitchReqCheck();
    bool SwitchAnimStateCheck();

    /* State */
    PageBase::State_t StateLoadExecute(PageBase* base);
    PageBase::State_t StateWillAppearExecute(PageBase* base);
    PageBase::State_t StateDidAppearExecute(PageBase* base);
    PageBase::State_t StateWillDisappearExecute(PageBase* base);
    PageBase::State_t StateDidDisappearExecute(PageBase* base);
    PageBase::State_t StateUnloadExecute(PageBase* base);
    void StateUpdate(PageBase* base);
    PageBase::State_t GetState()
    {
        return _PageCurrent->priv.State;
    }

private:

    /* 页面工厂 */
    PageFactory* _Factory;

    /* 页面池 */
    std::vector<PageBase*> _PagePool;

    /* 页面堆栈 */
    std::stack<PageBase*> _PageStack;

    /* 前一页面 */
    PageBase* _PagePrev;

    /* 当前页面 */
    PageBase* _PageCurrent;

    /* 页面动画状态 */
    struct
    {
        bool IsSwitchReq;              // 是否有切换请求
        bool IsBusy;                   // 是否正在切换
        bool IsEntering;               // 是否正在执行进入动作

        PageBase::AnimAttr_t Current;  // 当前动画属性
        PageBase::AnimAttr_t Global;   // 全局动画属性
    } _AnimState;

    /* 根节点样式 */
    lv_style_t* _RootDefaultStyle;
};

#endif
