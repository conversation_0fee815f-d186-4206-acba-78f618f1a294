/*
 * MIT License
 * Copyright (c) 2021 _VIFEXTech
 * C Language Port - Drag System Implementation
 */
#include "page_manager_c.h"
#include "pm_log_c.h"

/* 拖拽回调函数 */
static void pm_page_manager_on_root_drag_event(lv_event_t* event);
static void pm_page_manager_on_root_drag_anim_finish(lv_anim_t* a);
static void pm_page_manager_on_root_async_leave(void* page);

/**
 * @brief  启用根对象的拖拽功能
 * @param  manager: 页面管理器指针
 * @param  root: 根对象指针
 * @retval None
 */
void pm_page_manager_root_enable_drag(pm_page_manager_t* manager, lv_obj_t* root)
{
    if (manager == NULL || root == NULL) {
        return;
    }
    
    pm_page_base_t* page = (pm_page_base_t*)lv_obj_get_user_data(root);
    if (page == NULL) {
        PM_LOG_ERROR("Page base is NULL");
        return;
    }
    
    /* 为根对象添加拖拽事件回调 */
    lv_obj_add_event_cb(
        root,
        pm_page_manager_on_root_drag_event,
        LV_EVENT_ALL,
        page
    );
    
    PM_LOG_INFO("Page(%s) Root drag enabled", page->name);
}

/**
 * @brief  页面拖拽事件回调
 * @param  event: 事件结构体指针
 * @retval None
 */
static void pm_page_manager_on_root_drag_event(lv_event_t* event)
{
    lv_event_code_t event_code = lv_event_get_code(event);
    
    /* 只处理按下、按压和释放事件 */
    if (!(event_code == LV_EVENT_PRESSED || event_code == LV_EVENT_PRESSING || event_code == LV_EVENT_RELEASED)) {
        return;
    }
    
    lv_obj_t* root = lv_event_get_current_target(event);
    pm_page_base_t* page = (pm_page_base_t*)lv_event_get_user_data(event);
    
    if (page == NULL) {
        PM_LOG_ERROR("Page base is NULL");
        return;
    }
    
    pm_page_manager_t* manager = page->manager;
    if (manager == NULL) {
        PM_LOG_ERROR("Page manager is NULL");
        return;
    }
    
    pm_load_anim_attr_t anim_attr;
    
    /* 获取当前动画属性 */
    if (!pm_page_manager_get_current_load_anim_attr(manager, &anim_attr)) {
        PM_LOG_ERROR("Can't get current anim attr");
        return;
    }
    
    /* 如果不支持拖拽，直接返回 */
    if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_NONE) {
        return;
    }
    
    /* 如果动画正在执行，不允许拖拽 */
    if (manager->anim_state.is_busy) {
        return;
    }
    
    static lv_coord_t last_x = 0, last_y = 0;
    static bool is_dragging = false;
    
    lv_indev_t* indev = lv_indev_get_act();
    lv_point_t point;
    lv_indev_get_point(indev, &point);
    
    switch (event_code) {
    case LV_EVENT_PRESSED:
        last_x = point.x;
        last_y = point.y;
        is_dragging = false;
        PM_LOG_INFO("Drag pressed at (%d, %d)", point.x, point.y);
        break;
        
    case LV_EVENT_PRESSING:
        if (!is_dragging) {
            lv_coord_t diff_x = point.x - last_x;
            lv_coord_t diff_y = point.y - last_y;
            
            /* 判断拖拽方向 */
            if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_HOR && abs(diff_x) > 20) {
                is_dragging = true;
                PM_LOG_INFO("Start horizontal drag");
            } else if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_VER && abs(diff_y) > 20) {
                is_dragging = true;
                PM_LOG_INFO("Start vertical drag");
            }
        }
        
        if (is_dragging) {
            /* 执行拖拽 */
            if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_HOR) {
                lv_coord_t new_x = point.x - last_x;
                /* 限制拖拽范围 */
                if (new_x > LV_HOR_RES / 3) new_x = LV_HOR_RES / 3;
                if (new_x < -LV_HOR_RES / 3) new_x = -LV_HOR_RES / 3;
                lv_obj_set_x(root, new_x);
            } else if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_VER) {
                lv_coord_t new_y = point.y - last_y;
                /* 限制拖拽范围 */
                if (new_y > LV_VER_RES / 3) new_y = LV_VER_RES / 3;
                if (new_y < -LV_VER_RES / 3) new_y = -LV_VER_RES / 3;
                lv_obj_set_y(root, new_y);
            }
        }
        break;
        
    case LV_EVENT_RELEASED:
        if (is_dragging) {
            PM_LOG_INFO("Drag released");
            
            /* 判断是否需要切换页面 */
            bool should_switch = false;
            lv_coord_t current_pos = 0;
            lv_coord_t threshold = 0;
            
            if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_HOR) {
                current_pos = lv_obj_get_x(root);
                threshold = LV_HOR_RES / 4;
            } else if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_VER) {
                current_pos = lv_obj_get_y(root);
                threshold = LV_VER_RES / 4;
            }
            
            should_switch = (abs(current_pos) > threshold);
            
            if (should_switch) {
                /* 执行页面切换 */
                PM_LOG_INFO("Drag threshold reached, switching page");
                
                /* 异步执行页面切换，避免在事件回调中直接操作 */
                lv_async_call(pm_page_manager_on_root_async_leave, page);
            } else {
                /* 回弹到原位置 */
                PM_LOG_INFO("Drag threshold not reached, bouncing back");
                
                lv_anim_t a;
                pm_page_manager_anim_default_init(manager, &a);
                lv_anim_set_var(&a, root);
                lv_anim_set_ready_cb(&a, pm_page_manager_on_root_drag_anim_finish);
                lv_anim_set_user_data(&a, manager);
                
                if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_HOR) {
                    lv_anim_set_exec_cb(&a, (lv_anim_exec_xcb_t)lv_obj_set_x);
                    lv_anim_set_values(&a, current_pos, 0);
                } else if (anim_attr.drag_dir == PM_ROOT_DRAG_DIR_VER) {
                    lv_anim_set_exec_cb(&a, (lv_anim_exec_xcb_t)lv_obj_set_y);
                    lv_anim_set_values(&a, current_pos, 0);
                }
                
                manager->anim_state.is_busy = true;
                lv_anim_start(&a);
            }
        }
        is_dragging = false;
        break;
        
    default:
        break;
    }
}

/**
 * @brief  拖拽动画结束事件回调
 * @param  a: 动画指针
 * @retval None
 */
static void pm_page_manager_on_root_drag_anim_finish(lv_anim_t* a)
{
    if (a == NULL) {
        return;
    }
    
    pm_page_manager_t* manager = (pm_page_manager_t*)lv_anim_get_user_data(a);
    if (manager == NULL) {
        return;
    }
    
    PM_LOG_INFO("Root drag anim finish");
    manager->anim_state.is_busy = false;
    
    /* 隐藏底部页面 */
    pm_page_base_t* bottom_page = pm_page_manager_get_stack_top_after(manager);
    if (bottom_page && bottom_page->root) {
        lv_obj_add_flag(bottom_page->root, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief  异步离开页面回调
 * @param  page: 页面指针
 * @retval None
 */
static void pm_page_manager_on_root_async_leave(void* page)
{
    pm_page_base_t* base = (pm_page_base_t*)page;
    if (base == NULL || base->manager == NULL) {
        return;
    }
    
    PM_LOG_INFO("Async leave page: %s", base->name);
    
    /* 执行页面弹出操作 */
    pm_page_manager_pop(base->manager);
}
